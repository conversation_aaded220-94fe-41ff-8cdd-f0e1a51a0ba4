import base64
import traceback

from django.core.paginator import Paginator
from django.core.files.storage import default_storage

from doctor_home_care_crm.settings import FERNET_KEY
from utilities.logs_manager import LogsManager
from utilities.modelMapping import MODEL_MAPPING
from cryptography.fernet import Fernet
import decimal
from django.db import models
from decimal import Decimal

from utilities.status_code import StatusCode
from utilities.token_manager import TokenManager
from utilities import messages
from utilities import status_code
import utilities.parameters as params
import traceback
from django.db.models.fields.files import FieldFile
from django.db.models import Q



default_language = params.default_language
messages = messages.Messages()
status_code_obj = status_code.StatusCode()
token_manager = TokenManager()
logs_manager = LogsManager(params.logs_dir)
en_or_gr = 'el'

#from app_communications.models import *


def pagination(request, list_data, page_size):
    # Set up pagination
    paginator = Paginator(list_data, page_size)  # Show 10 objects per page
    page_number = request.GET.get('page')  # Get the page number from the URL
    page_obj = paginator.get_page(page_number)  # Get the objects for the current page

    return page_obj


def prepare_filters_and_context(query_params, tablename):
    filters = {}
    context_filters = {}
    custom_q_filters = Q()

    for key, value in query_params.items():
        if value == "":
            continue

        base_key = key.replace("__contains", "").replace("__gte", "").replace("__lte", "")
        context_filters[base_key] = value

        if key == "user__contains":
            # Custom logic: search across multiple user fields
            custom_q_filters &= (
                Q(user__first_name__icontains=value) |
                Q(user__last_name__icontains=value) |
                Q(user__email__icontains=value)
            )
        elif key.endswith("__contains"):
            filters[key] = value
        elif key.endswith("__gte"):
            filters[key] = value
        elif key.endswith("__lte"):
            filters[key] = value
        else:
            filters[key] = value  # exact match

    return filters, context_filters, custom_q_filters



def prepare_order(request, order_by_param):
    field, method = order_by_param.split('__')  # Example: "rating__desc"
    ordering = f'-{field}' if method == 'desc' else field
    return ordering


def delete_files_before(tablename, id, obj):
    # Delete files that are connected with this object
    if 'File' not in tablename:
        model_class = MODEL_MAPPING.get(tablename)
        if hasattr(obj, 'files') and obj.files.exists():
            files = obj.files.all()
            # delete files
            for file in files:
                print(f"File: {file.file.url}, Description: {file.description}")
                file.file.delete(save=False)
    # Delete photo from disk
    if hasattr(obj, 'fotografia') and obj.fotografia:
        # Delete fotografia
        delete_file_by_url(obj.fotografia.url)
    if hasattr(obj, 'logo') and obj.logo:
        # Delete logo
        delete_file_by_url(obj.logo.url)
    #Delete specific file
    if hasattr(obj, 'file') and obj.file:
        obj.file.delete(save=False)

def do_before_delete(tablename, id, obj):
    return;


# Διάγραψε το αρχείο μέσω URL
def delete_file_by_url(file_url):
    # Αφαίρεσε το base URL (αν υπάρχει)
    relative_path = file_url.replace('/media/', '')  # Αν το MEDIA_URL είναι '/media/'

    # Εντοπίζεις και διαγράφεις το αρχείο
    if default_storage.exists(relative_path):
        default_storage.delete(relative_path)
        print(f"Το αρχείο {relative_path} διαγράφηκε.")
    else:
        print("Το αρχείο δεν βρέθηκε.")


def find_redirect_page(tablename, list_that_uses_for_params, params):
    if tablename == 'ResumeFile':
        params['viografiko_id'] = get_value_from_list(list_that_uses_for_params, 'viografiko_id')
        return 'resume_files', ['viografiko_id'], params
    elif tablename == 'Event':
        return 'get_events', None, None
    else:
        return None, None, None


def get_value_from_list(param_list, search_key):
    dict_param_list = dict(param_list)
    return dict_param_list.get(search_key, None)


# Encrypt the file path
def encrypt_filepath(id, tablename):
    # Encrypt
    fernet = Fernet(FERNET_KEY)
    reference = f"{id}:{tablename}"
    encrypted = fernet.encrypt(reference.encode()).decode()
    return encrypted

# Decrypt the file path
def decrypt_filepath(encrypted):
    # Decrypt
    try:
        fernet = Fernet(FERNET_KEY)
        reference = fernet.decrypt(encrypted.encode()).decode()
        id, tablename = reference.split(':')

        # Ανάκτηση του μοντέλου δυναμικά
        model_class = MODEL_MAPPING.get(tablename)
        obj = model_class.objects.get(id=id)
        return obj
    except Exception as e:
        raise ValueError("Η αποκρυπτογράφηση απέτυχε ή το αντικείμενο δεν βρέθηκε.")


def do_after_save(new_obj, file_tablename):
    if file_tablename == 'ResumeFile':
        if new_obj.encrypted_file_path is None:
            new_obj.encrypted_file_path = encrypt_filepath(new_obj.id, file_tablename)
    # if file_tablename == 'Communications':
    #     if new_obj.sms_template_id is not None:
    #         template = SmsTemplate.objects.filter(id=new_obj.sms_template_id).first()
    #         new_obj.template = template.name
    new_obj.save()
    return new_obj

def find_list_that_used_for_params(tablename, obj):
    returnlist=[]
    if tablename == 'ResumeFile':
        returnlist.append(('viografiko_id', obj.resume.id))
    if tablename == 'Interview':
        returnlist.append(('viografiko_id', obj.resume.id))
    return returnlist


def set_multiple_file_urls(json_obj, obj, attr_names):
    """Ορίζει πολλά file URLs στο json_obj με βάση τα attribute names."""
    for attr in attr_names:
        file = getattr(obj, attr, None)
        json_obj[attr] = file.url if file and hasattr(file, 'url') else None

def set_all_file_urls(json_obj, obj):
    """Automatically sets file URLs for all FileField/ImageField fields."""
    for field in obj._meta.fields:
        val = getattr(obj, field.name)
        if isinstance(val, FieldFile):
            json_obj[field.name] = val.url if val and val.name else None

def createOrUpdate(request, form_data):
    try:
        tablename = form_data.get('tablename')
        model_class = MODEL_MAPPING.get(tablename)
        if not model_class:
            raise ValueError(f"Model '{tablename}' is not defined in MODEL_MAPPING")
        if model_class:
            print(f"Model class: {model_class}")
        id = form_data.get('id', None)
        status, message = '',''
        if id:  # Update
            obj = model_class.objects.get(id=id)

            # Update the Employee's information
            for key, value in form_data.items():
                if key == 'tablename' or key == 'csrfmiddlewaretoken': continue
                # print(f"key: {key} , value: {value}")
                # print(f"Field type for {key}: {type(getattr(obj, key))}")
                field = model_class._meta.get_field(key)
                if hasattr(obj, key) and key != 'id':  # Skip setting the 'id' field
                    if isinstance(field, models.DecimalField):
                        try:
                            value = Decimal(value.strip()) if value.strip() else None
                        except decimal.InvalidOperation:
                            value = None  # Handle invalid decimal values
                    if isinstance(field, models.IntegerField):
                        try:
                            value = int(value.strip()) if value.strip() else None
                        except ValueError:
                            value = None  # Handle invalid decimal values
                    if isinstance(field, models.DateField) or isinstance(field, models.DateTimeField):
                        value = value.strip()
                        value = value if value else None

                    setattr(obj, key, value)

            obj.save()
            print(f"update {model_class} with id: {obj.id} and administrator_id: {obj.administrator_id}")
            status, message = StatusCode.ok_200, f"update {model_class} with id: {obj.id} and administrator_id: {obj.administrator_id}"
        else:  # Create
            table_fields = [field.name for field in model_class._meta.get_fields()]
            filtered_data = {
                key: value for key, value in form_data.items()
                if key in table_fields and key != 'id'  # Exclude 'id'
            }

            # If field has a different type of Char
            for key, value in filtered_data.items():
                field = model_class._meta.get_field(key)
                # print(f"Field type for {key}: {type(field)}")
                if isinstance(field, models.DecimalField):
                    try:
                        value = Decimal(value.strip()) if value.strip() else None
                    except decimal.InvalidOperation:
                        value = None  # Handle invalid decimal values
                if isinstance(field, models.IntegerField):
                    try:
                        value = int(value.strip()) if value.strip() else None
                    except ValueError:
                        value = None
                if isinstance(field, models.DateField) or isinstance(field, models.DateTimeField):
                    value = value.strip()
                    value = value if value else None

                # Handle invalid decimal values

                filtered_data[key] = value

            filtered_data['administrator_id'] = request.user.administrator_id
            # print("create event with administrator_id: " + str(request.user.administrator_id))
            new_obj = model_class.objects.create(**filtered_data)
            new_obj.save()
            print(f"Creating new record for administratorId {request.user.administrator_id}")
            status, message = StatusCode.created_201, f"Creating new record for administratorId {request.user.administrator_id}"
    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        return StatusCode.internal_server_error_500, traceback.format_exc()

    return status, message

def prepare_image_preview_data(obj, json_obj):
    """
    Prepares image data for preview in edit forms.
    Adds image URLs to the JSON object for all ImageFields in the model.
    """
    # Process all ImageFields in the model
    for field in obj._meta.fields:
        if isinstance(field, models.ImageField):
            image_field = getattr(obj, field.name)
            if image_field and hasattr(image_field, 'url'):
                json_obj[field.name] = image_field.url
            else:
                json_obj[field.name] = None
    
    return json_obj
