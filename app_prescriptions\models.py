from django.db import models

class Prescription(models.Model):
    administrator_id = models.IntegerField(null=False, blank=False)
    patient = models.ForeignKey('app_patients.Patient', on_delete=models.CASCADE, related_name='prescriptions')
    date = models.DateField(auto_now_add=True)  
    barcode_number = models.CharField(max_length=256)
    prescriber_name = models.CharField(max_length=256)
    pharmacy_name = models.CharField(max_length=256)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        
    def __str__(self):
        return f"Prescription for {self.patient} on {self.date}"
