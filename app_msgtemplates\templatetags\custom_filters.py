# # app_msgtemplates/templatetags/custom_filters.py

# from django import template
# from django.utils.html import strip_tags
# from django.utils.safestring import mark_safe

# register = template.Library()

# @register.filter
# def truncatechars_html(value, max_length):
#     plain_text = strip_tags(value)
#     if len(plain_text) > max_length:
#         plain_text = plain_text[:max_length].rstrip() + "..."
#     return mark_safe(f"<p>{plain_text}</p>")

# @register.filter
# def get(obj, attr):
#     """Returns an attribute of an object dynamically from a string"""
#     if obj is None:
#         return None

#     # Support nested attributes like "host.name"
#     if isinstance(attr, str) and '.' in attr:
#         for part in attr.split('.'):
#             obj = getattr(obj, part, None)
#             if obj is None:
#                 return None
#         return obj

#     if isinstance(obj, dict):
#         return obj.get(attr)
    
#     return getattr(obj, attr, None)
