from django import template
import json
import re
import locale
from datetime import date


register = template.Library()

@register.filter
def get(obj, attr):
    """
    Supports nested attribute access using dot (.) or double underscore (__)
    e.g. patient__first_name or patient.first_name
    """
    if obj is None:
        return None

    # Support both dot notation and Django-style double underscore
    parts = attr.replace('.', '__').split('__')
    for part in parts:
        if isinstance(obj, dict):
            obj = obj.get(part)
        else:
            obj = getattr(obj, part, None)
        if obj is None:
            return ""
    return obj

@register.filter
def jsonify(value):
    """
    Converts a Python object to JSON string
    
    Usage: {{ data|jsonify }}
    """
    return json.dumps(value)

@register.filter
def get_item(dictionary, key):
    """
    Gets an item from a dictionary using the key
    
    Usage: {{ mydict|get_item:key_variable }}
    """
    if not dictionary:
        return None
    
    if hasattr(dictionary, key):
        return getattr(dictionary, key)
    
    try:
        return dictionary.get(key, None)
    except (AttributeError, TypeError):
        try:
            return dictionary[key]
        except (KeyError, TypeError):
            return None

@register.filter
def default_if_none(value, default=""):
    """
    Returns the default value if the provided value is None
    
    Usage: {{ value|default_if_none:"N/A" }}
    """
    if value is None:
        return default
    return value

@register.filter
def format_date(value, format_string="%d/%m/%Y"):
    """
    Formats a date using the specified format string
    
    Usage: {{ date_value|format_date:"%Y-%m-%d" }}
    """
    if not value:
        return ""
    try:
        return value.strftime(format_string)
    except (AttributeError, ValueError):
        return value

@register.filter
def is_image_field(field_value):
    """
    Checks if a field value is an image field
    
    Usage: {{ field_value|is_image_field }}
    """
    if hasattr(field_value, 'url'):
        file_name = str(field_value)
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
        return any(file_name.lower().endswith(ext) for ext in image_extensions)
    return False

@register.filter
def get_file_type(file):
    """Determine if a file is an image based on its content type"""
    if hasattr(file, 'content_type') and file.content_type:
        return 'image' if file.content_type.startswith('image') else 'file'
    return 'file'

@register.filter
def get_filename(value):
    return value.split('/')[-1]

@register.filter
def format_action_onclick(onclick_string, obj):
    """
    Replace placeholders in onclick string with actual values from the object.
    Example: "openDeleteModal('hosts', '{id}', '{name}')" becomes "openDeleteModal('hosts', '1', 'Example Name')"
    """
    if not onclick_string:
        return ''
    
    # Find all placeholders in the format {field_name}
    placeholders = re.findall(r'\{([^}]+)\}', onclick_string)
    
    # Replace each placeholder with the corresponding value from the object
    result = onclick_string
    for field in placeholders:
        try:
            # Get the value from the object
            if hasattr(obj, field):
                value = getattr(obj, field)
            elif hasattr(obj, 'get') and callable(obj.get):
                value = obj.get(field, '')
            elif isinstance(obj, dict):
                value = obj.get(field, '')
            else:
                value = ''
                
            # Replace the placeholder with the value
            result = result.replace('{' + field + '}', str(value))
        except Exception as e:
            print(f"Error replacing placeholder {field}: {e}")
    
    return result

@register.filter
def format_date_gr(value):
    """
    Format a date in Greek like '5 Ιουνίου 2025'
    """
    if not value:
        return ""
    
    # Greek month names in genitive case
    months_gr = {
        1: "Ιανουαρίου",
        2: "Φεβρουαρίου",
        3: "Μαρτίου",
        4: "Απριλίου",
        5: "Μαΐου",
        6: "Ιουνίου",
        7: "Ιουλίου",
        8: "Αυγούστου",
        9: "Σεπτεμβρίου",
        10: "Οκτωβρίου",
        11: "Νοεμβρίου",
        12: "Δεκεμβρίου"
    }

    try:
        day = value.day
        month = months_gr[value.month]
        year = value.year
        return f"{day} {month} {year}"
    except Exception as e:
        return value.strftime("%d/%m/%Y")
