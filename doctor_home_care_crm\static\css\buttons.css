.add-btn {
    background-color: #65cfc4;
    color: white;
    border-radius: 999px;
    padding: 0 14px;
    font-weight: bold;
    font-size: 1rem;
    border: none;
    display: inline-flex;
    align-items: center;
    /* gap: 8px; */
}

.add-btn i {
    font-size: 1rem;
}

.add-btn:hover {
    background-color: #56bdb3;
}

.delete-edit-btn {
    background-color: #65cfc4;
    color: white;
    border-radius: 999px;
    padding: 0.5rem 1rem;
    font-weight: bold;
    font-size: 1rem;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center; /* ✅ Add this */
    text-align: center;       /* ✅ Add this */
    transition: background-color 0.3s ease;
}


.delete-edit-btn:hover {
    background-color: #56bdb3;
}

.delete-btn {
    background-color: #65cfc4;
    color: white;
    border-radius: 999px;
    padding: 0.5rem 1rem;
    font-weight: bold;
    font-size: 1rem;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center; /* ✅ Add this */
    text-align: center;       /* ✅ Add this */
    transition: background-color 0.3s ease;
}


.delete-btn:hover {
    background-color: #56bdb3;
}

.btn-dark, 
.btn-outline-dark {
    background-color: #212529 !important;
    border-color: #212529 !important;
    color: white !important;
}

.btn-dark:hover, 
.btn-dark:focus,
.btn-outline-dark:hover,
.btn-outline-dark:focus {
    background-color: #7FEFDF!important;
    border-color: #000000 !important;
    color: #424242 !important;
}

.btn-outline-dark {
    background-color: rgb(246, 246, 246) !important;
    color: #000000 !important;
}

/* Rounded button with hover effect */
.custom-icon-btn {
    border-radius: 999px;
    padding: 6px 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border: 1px solid #ccc;
    transition: background-color 0.2s ease;
}

.custom-icon-btn:hover {
    background-color: #f2f2f2;
}

/* Mint icon color for edit */
.custom-icon-mint {
    color: #56bdb3;
    font-size: 1rem;
}

/* Red icon color for delete */
.custom-icon-red {
    color: #dc3545;
    font-size: 1rem;
}

.custom-icon-btn:hover .custom-icon-mint {
    color: #3daaa1;
}

.custom-icon-btn:hover .custom-icon-red {
    color: #c82333;
}

.custom-columns-btn {
  color: #65cfc4;
  font-weight: 800;
  font-size: 1.1rem;
  border: none;
  border-radius: 999px;
  display: inline-flex;
  align-items: center;
  transition: background-color 0.3s ease;
}

.custom-columns-btn i {
  font-size: 1.4rem;
}

.custom-columns-btn:hover {
  color: #56bdb3;
}

.medical_note_btn {
    background-color: white;
    color: black;
    transition: background-color 0.5s ease;
    border: 1px solid black;
}

.medical_note_btn:hover {
    background-color: black;
    color: white;
}

    /* Mint checkbox styling */
.column-toggle[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #6AC7BA;
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    position: relative;
    background-color: #fff;
    transition: background-color 0.2s, border-color 0.2s;
}

/* Checked style: mint background and checkmark */
.column-toggle[type="checkbox"]:checked {
    background-color: #6AC7BA;
    border-color: #6AC7BA;
}

.column-toggle[type="checkbox"]:checked::after {
    /* content: '✓'; */
    color: rgb(240, 240, 240);
    font-size: 14px;
    position: absolute;
    top: 0;
    left: 3px;
    font-weight: bold;
    
}

/* Improve spacing */
.column-row {
    font-weight: 500;
    font-size: 15px;
    padding-top: 12px;
    padding-bottom: 12px;
}

.btn-prof-hover:hover {
    background-color: #E4FCF9;
}


/* Style the select itself */
select.custom-dropdown {
    background-color: white;
    border: 2px solid #6AC7BA;
    border-radius: 50px;
    padding: 0.375rem 1.25rem;
    color: #333;
    font-weight: 500;
    appearance: none;         /* Remove default browser dropdown */
    -webkit-appearance: none; /* Safari */
    -moz-appearance: none;    /* Firefox */
}

/* Remove default focus ring */
select.custom-dropdown:focus {
    outline: none;
    box-shadow: none;
    border-color: #6AC7BA;
}

/* Optional: mimic dropdown arrow */
select.custom-dropdown {
    background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cpath fill='%236AC7BA' d='M0 0l5 6 5-6z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 0.6em auto;
}

/* Ensure the custom class applies visible radius on top-left and bottom-left */
.custom-offcanvas-rounded {
    border-top-left-radius: 1rem !important;
    border-bottom-left-radius: 1rem !important;
    overflow: hidden;
}

/* Ensure the offcanvas has visible background */
.offcanvas {
    background-color: #fff; /* or your custom color */
}

/* body.offcanvas-backdrop, .modal-backdrop {
    overflow: visible !important;
} */
.mobile-profile-dropdown {
    background-color: #e0fdfb;
    border-radius: 12px;
    padding: 12px 16px;
    margin: 1rem 0;
    position: relative;
}

.mobile-profile-dropdown .dropdown {
    width: 100%;
}

.mobile-profile-dropdown .dropdown-toggle {
    width: 100%;
    background: none;
    border: none;
    font-weight: 600;
    color: #000;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0;
}

.mobile-profile-dropdown .dropdown-menu {
    position: static !important;
    float: none;
    width: 100%;
    margin-top: 0.5rem;
    box-shadow: none;
    border: none;
    background-color: transparent;
}
