from django.http import JsonResponse
from django.forms.models import model_to_dict
from utilities.services_app import MODEL_MAPPING, prepare_image_preview_data, set_multiple_file_urls
import traceback
from utilities.logs_manager import LogsManager
from django.contrib.auth.decorators import login_required
import utilities.parameters as params
import json
from django.db import models
from decimal import Decimal
from utilities.utils_db import get_object_json_serializable
from app_users.models import User
from utilities.services_app import set_all_file_urls
from app_partners.models import Partner



logs_manager = LogsManager(params.logs_dir)

@login_required
def find_obj_with_id(request):
    try:
        obj_id = request.GET.get('obj_id')
        tablename = request.GET.get('tablename')
        fieldname = request.GET.get('fieldname')
        
        print(f"find_obj_with_id: {fieldname}={obj_id} in {tablename}")
        
        model_class = MODEL_MAPPING.get(tablename)
        if not model_class:
            return JsonResponse({'status': 'error', 'message': f'Model {tablename} not found'})
            
        # Filter and retrieve the object
        filter_kwargs = {fieldname: obj_id}
        obj = model_class.objects.filter(**filter_kwargs).first()
        if not obj:
            return JsonResponse({'status': 'error', 'message': f'Object not found with {fieldname}={obj_id}'})
        
        # Convert object to dict
        json_obj = model_to_dict(obj, exclude=['password'] if tablename == 'User' else [])
        
        # Automatically append .url for all file fields
        set_all_file_urls(json_obj, obj)

        return JsonResponse({'status': 'success', 'data': json_obj})
    
    except Exception as e:
        traceback.print_exc()
        # Optional: logs_manager.write_error_logs(traceback.format_exc())
        return JsonResponse({'status': 'error', 'message': str(e)})
    
def find_objects_with_administratorId_and_userId(request):
    try:
        if request.method == 'POST':
            form_data = {key: value.strip() for key, value in request.POST.items()}
            tablename = form_data.get('tablename')
            model_class = MODEL_MAPPING.get(tablename)
            if model_class:
                objects = model_class.objects.filter(administrator_id=request.user.administrator_id, user_id=request.user.id)
                
                # Use the utility function to serialize the queryset
                objects_data = get_object_json_serializable(objects)
                
                return JsonResponse({'status': 'success', 'data': objects_data})
            else:
                return JsonResponse({'status': 'error', 'message': 'Model not found!'})
        else:
            return JsonResponse({'status': 'error', 'message': 'Invalid request method!'})
    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        return JsonResponse({'status': 'error', 'message': str(e)})

def update_field(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            model_name = data.get('model')
            obj_id = data.get('id')
            field = data.get('field')
            value = data.get('value')

            model_class = MODEL_MAPPING.get(model_name)
            if not model_class:
                return JsonResponse({'status': 'error', 'message': f"Model '{model_name}' not found."})

            obj = model_class.objects.get(id=obj_id)
            if not hasattr(obj, field):
                return JsonResponse({'status': 'error', 'message': f"Field '{field}' does not exist."})

            field_obj = model_class._meta.get_field(field)

            if isinstance(field_obj, models.BooleanField):
                value = value in ['true', 'True', '1']
            elif isinstance(field_obj, models.IntegerField):
                value = int(value)
            elif isinstance(field_obj, models.DecimalField):
                value = Decimal(value)
            elif isinstance(field_obj, models.FloatField):
                value = float(value)

            setattr(obj, field, value)
            obj.save()

            return JsonResponse({'status': 'success', 'message': 'Field updated successfully'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})

    return JsonResponse({'status': 'error', 'message': 'Invalid request method'})


def check_email_exists(request):
    try:
        email = request.GET.get('email', '').strip()
        model_name = request.GET.get('model', '').strip()
        obj_id = request.GET.get('id', '').strip()

        if not email or model_name not in MODEL_MAPPING:
            return JsonResponse({'exists': False})

        Model = MODEL_MAPPING[model_name]
        qs = Model.objects.filter(email__iexact=email)

        if obj_id.isdigit():
            qs = qs.exclude(id=obj_id)

        exists = qs.exists()
        return JsonResponse({'exists': exists})

    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'exists': False, 'error': str(e)})

def check_tin_exists(request):
    try:
        tin = request.GET.get('tin', '').strip()
        model_name = request.GET.get('model', '').strip()
        obj_id = request.GET.get('id', '').strip()

        if not tin or model_name not in MODEL_MAPPING:
            return JsonResponse({'exists': False})

        Model = MODEL_MAPPING[model_name]
        
        # Filter by administrator_id to check uniqueness per administrator
        qs = Model.objects.filter(
            tin__iexact=tin,
            administrator_id=request.user.administrator_id
        )

        if obj_id.isdigit():
            qs = qs.exclude(id=obj_id)

        exists = qs.exists()
        return JsonResponse({'exists': exists})
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'exists': False, 'error': str(e)})

def check_amka_exists(request):
    try:
        amka = request.GET.get('amka', '').strip()
        model_name = request.GET.get('model', '').strip()
        obj_id = request.GET.get('id', '').strip()

        if not amka or model_name not in MODEL_MAPPING:
            return JsonResponse({'exists': False})

        Model = MODEL_MAPPING[model_name]
        
        # Filter by administrator_id to check uniqueness per administrator
        qs = Model.objects.filter(
            amka__iexact=amka,
            administrator_id=request.user.administrator_id
        )

        # Exclude current object if editing
        if obj_id.isdigit():
            qs = qs.exclude(id=obj_id)

        exists = qs.exists()
        return JsonResponse({'exists': exists})

    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'exists': False, 'error': str(e)})
