version: '3.8'

services:
  doctor_home_care_crm_django:
    build:
      context: ..
      dockerfile: docker_doctor_home_care_crm/Dockerfile
    container_name: doctor_home_care_crm_django
    restart: always
    environment:
      DB_TYPE: postgres
      DB_HOST: postgres_main
      DB_PORT: 5432
      DB_USER: doctorhomecareuser
      DB_PASS: e5zT0EW5H8Sj98feh45ytYYYYhIhHi
      DB_NAME: doctor_home_care_crmdb
    #env_file:
    #  - ../.env
    volumes:
      - ../:/app
      - static_volume:/app/static
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.doctor_home_care_crm.rule=Host(`crm.doctorhomecare.gr`)"  # ✅ Fixed quotes
      - "traefik.http.routers.doctor_home_care_crm.entrypoints=websecure"
      - "traefik.http.routers.doctor_home_care_crm.tls.certresolver=cloudflare_resolver"  # ✅ Correct resolver
    networks:
      - proxy

networks:
  proxy:
    external: true

volumes:
  static_volume:
  doctor_home_care_crm_dbdata:
