ADD_MODAL_ID = 'addModalId'
EDIT_MODAL_ID = 'editModalId'

COLUMNS = [
    {
        'id': 'id',
        'label': '#',
        'field': 'id',
        'type': 'counter',
        'required': True,
        'default_visible': True
    },
    {
        'id': 'patient_first_name',
        'label': 'ΟΝΟΜΑ',
        'field': 'patient__first_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'patient_last_name',
        'label': 'ΕΠΩΝΥΜΟ',
        'field': 'patient__last_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'date',
        'label': 'ΗΜΕΡΟΜΗΝΙΑ',
        'field': 'date',
        'type': 'date',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'barcode_number',
        'label': 'ΑΡΙΘΜΟΣ BARCODE',
        'field': 'barcode_number',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'prescriber_name',
        'label': 'ΣΥΝΤΑΓΟΓΡΑΦΩΝ',
        'field': 'prescriber_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'pharmacy_name',
        'label': 'ΦΑΡΜΑΚΕΙΟ',
        'field': 'pharmacy_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'actions',
        'label': 'ΕΝΕΡΓΕΙΕΣ',
        'type': 'actions',
        'required': True,
        'default_visible': True
    }
]

FILTERS = [
    {
        'id': 'order_by',
        'name': 'order_by',
        'label': 'Ταξινόμηση',
        'field': 'order_by',
        'type': 'select',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
        },
        'options': [
            {'value': 'created_at__asc', 'label': 'Ημερομηνία Αύξουσα ↑'},
            {'value': 'created_at__desc', 'label': 'Ημερομηνία Φθίνουσα ↓'}
        ]
    },
    {
        'id': 'patient_first_name_filter',
        'name': 'patient__first_name__contains',
        'label': 'Όνομα Ασθενή',
        'field': 'patient__first_name',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'patient_last_name_filter',
        'name': 'patient__last_name__contains',
        'label': 'Επώνυμο Ασθενή',
        'field': 'patient__last_name',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'date_filter',
        'name': 'date',
        'label': 'Ημερομηνία',
        'field': 'date',
        'type': 'date',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'barcode_number_filter',
        'name': 'barcode_number__contains',
        'label': 'Αριθμός Barcode',
        'field': 'barcode_number',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'prescriber_name_filter',
        'name': 'prescriber_name__contains',
        'label': 'Συνταγογραφών',
        'field': 'prescriber_name',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'pharmacy_name_filter',
        'name': 'pharmacy_name__contains',
        'label': 'Φαρμακείο',
        'field': 'pharmacy_name',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    }
]

FORM_FIELDS = [
    {
        'id': 'patient',
        'label': 'Ασθενής',
        'type': 'select',
        'required': False,
        'width': '12',
        'placeholder': 'Επιλέξτε ασθενή',
        'options': []  # This will be populated dynamically in the view
    },
    {
        'id': 'barcode_number',
        'label': 'Αριθμός Barcode',
        'type': 'text',
        'required': False,
        'width': '6',
        'placeholder': 'Εισάγετε αριθμό barcode'
    },
    {
        'id': 'prescriber_name',
        'label': 'Συνταγογράφος',
        'type': 'text',
        'required': False,
        'width': '6',
        'placeholder': 'Εισάγετε όνομα συνταγογράφου'
    },
    {
        'id': 'pharmacy_name',
        'label': 'Φαρμακείο',
        'type': 'text',
        'required': False,
        'width': '6',
        'placeholder': 'Εισάγετε όνομα φαρμακείου'
    }
]
