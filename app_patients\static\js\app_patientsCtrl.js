    
// Function to open the delete modal and populate it with parameters
/**
 * Opens the edit modal for a specific model
 * @param {string} modelName - The model name (e.g., 'Host')
 * @param {string} idField - The ID field name (usually 'id')
 * @param {string|number} idValue - The ID value
 * @param {string} modalId - The modal ID (e.g., 'editHostModal')
 */

function openDeleteModal(tablename, id) {
    // Set modal content dynamically
    document.getElementById('tablename_delete_modal').value = tablename;
    document.getElementById('obj_id').value = id;
    // document.getElementById('description').textContent = description;

    console.log("deletemodal-->", tablename, " with id: ", id);

    // Use Bootstrap's modal API to show the modal
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
    
}


function openAddModal(modalId, modal_title, modal_description) {        
    // Reset form
    const form = document.getElementById('add_form_id');

    if (form) form.reset();
    
    // Clear hidden ID field
    const idInput = document.getElementById('id_add');
    if (idInput) idInput.value = '';
    
    // Clear image previews
    const previews = document.querySelectorAll(`#${modalId} img[id^="preview"]`);
    previews.forEach(preview => {
        preview.src = '';
        preview.style.display = 'none';
    });
    
    // Show modal
    const modalElement = document.getElementById(modalId);
    if (modalElement) {
        try {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } catch (error) {
            console.error('Error showing modal:', error);
            console.log('Modal element exists:', !!modalElement);
            console.log('Bootstrap loaded:', typeof bootstrap !== 'undefined');
            console.log('Bootstrap Modal loaded:', typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined');
        }
    } else {
        console.error(`Modal element with ID "${modalId}" not found`);
    }
}


/**
 * Opens the edit modal for a specific model
 * @param {string} modelName - The model name (e.g., 'Host')
 * @param {string} idField - The ID field name (usually 'id')
 * @param {string|number} idValue - The ID value
 * @param {string} modalId - The modal ID (e.g., 'editHostModal')
 */
function openEditModal(modelName, idField, idValue, modalId) {
    console.log('openEditModal called with:', modelName, idField, idValue, modalId);
    
    // Find the modal element
    const modalElement = document.getElementById(modalId);
    if (!modalElement) {
        console.error(`Modal element with ID "${modalId}" not found`);
        return;
    }
    
    // Make the AJAX request to get the object data
    fetch(`/find_obj_with_id/?obj_id=${idValue}&tablename=${modelName}&fieldname=${idField}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('Data received:', data.data);
                
                // Special handling for Event model
                if (modelName === 'Event') {
                    console.log('Using special handling for Event model');
                    
                    // Make sure the modal is visible before populating
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    
                    // Wait for modal to be fully shown
                    modalElement.addEventListener('shown.bs.modal', function onModalShown() {
                        console.log('Modal shown, populating fields');
                        populateEditEventModal(data.data);
                        // Remove the event listener to avoid multiple calls
                        modalElement.removeEventListener('shown.bs.modal', onModalShown);
                    }, { once: true });
                } else {
                    // Set ID field
                    const idInput = document.getElementById('id_edit');
                    if (idInput) {
                        idInput.value = idValue;
                    } else {
                        console.error('ID input element not found');
                    }
                    
                    // Set other fields dynamically
                    for (const key in data.data) {
                        const field = document.getElementById(`${key}_edit`);
                        if (field) {
                            console.log(`Setting field ${key}_edit to:`, data.data[key]);
                            
                            // Handle different field types
                            if (field.type === 'checkbox') {
                                field.checked = Boolean(data.data[key]);
                            } else if (field.type === 'file') {
                                // For file inputs, show preview if available
                                if (data.data[key]) {
                                    const previewImg = document.getElementById(`preview${key}_edit`);
                                    if (previewImg) {
                                        previewImg.src = data.data[key];
                                        previewImg.style.display = 'block';
                                    }
                                }
                            } else if (field.type === 'password') {
                                // For password fields, leave them empty
                                field.value = '';
                                // Add a placeholder to indicate that leaving it empty will keep the existing password in italic
                                field.placeholder = 'Αφήστε κενό για να διατηρήσετε τον υπάρχοντα κωδικό';
                            } else {
                                // For all other input types
                                if (data.data[key] !== null && data.data[key] !== undefined) {
                                    field.value = data.data[key];
                                } else {
                                    field.value = '';
                                }
                            }
                        }
                    }
                    
                    // Show the modal
                    const offcanvas = new bootstrap.Offcanvas(modalElement);
                    offcanvas.show();
                }
            } else {
                console.error('Error fetching data:', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function deleteFile1(modelName, objectId, fieldName) {
    console.log("Calling deleteFile...");
    console.log("modelName: " + modelName);
    console.log("objectId: " + objectId);
    console.log("fieldName: " + fieldName);
    
    // Store the parameters for later use
    if (typeof fileToDelete === 'undefined') {
        window.fileToDelete = {};
    }
    
    fileToDelete.modelName = modelName;
    fileToDelete.objectId = objectId;
    fileToDelete.fieldName = fieldName;
    
    // Get the modal element
    const modalElement = document.getElementById('deleteFileModal');
    if (!modalElement) {
        console.error('Delete file modal not found in the DOM');
        return;
    }
    
    // Set modal content for file deletion
    const modalLabel = document.getElementById('deleteFileModalLabel');
    if (modalLabel) {
        modalLabel.textContent = 'Διαγραφή Αρχείου';
    }
    
    // Add a custom attribute to identify this as a file deletion
    modalElement.setAttribute('data-delete-type', 'file');
    
    // Show the existing delete modal
    const deleteFileModal = new bootstrap.Modal(modalElement);
    deleteFileModal.show();
}

function uploadFile(modelName, objectId, fieldName, columnType) {
    console.log('Uploading file for:', modelName, objectId, fieldName, columnType); 
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.style.display = 'none';
    document.body.appendChild(fileInput);

    fileInput.click();

    fileInput.onchange = function() {
        const file = fileInput.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('field_name', fieldName);

            // Define the allowed MIME types for each column type
            const allowedMimeTypes = {
                'text': [],
                'textarea': [],
                'number': [],
                'datetime': [],
                'select': [],
                'image': ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'image/svg+xml', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
                'document': ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.oasis.opendocument.text', 'application/vnd.oasis.opendocument.spreadsheet', 'application/vnd.oasis.opendocument.presentation'],
                // Add more column types and their allowed MIME types as needed
            };

            // Check the file type based on the column type
            if (allowedMimeTypes[columnType] && !allowedMimeTypes[columnType].includes(file.type)) {
                alert(`Please select a valid file for the ${fieldName} field.`);
                return;
            }

            fetch(`/patients/${modelName}/${objectId}/upload_file/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessToast('Το αρχείο ανέβηκε επιτυχώς!');
                    location.reload();
                } else {
                    showErrorToast('Error uploading file: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorToast('An error occurred while uploading the file.');
            });
        }
        document.body.removeChild(fileInput);
    };
}

// Override the form submission in the delete modal
document.addEventListener('DOMContentLoaded', function() {
    const deleteForm = document.querySelector('#deleteFileModal form');
    if (deleteForm) {   
        deleteForm.addEventListener('submit', function(e) {
            // Check if this is a file deletion
            const deleteType = document.getElementById('deleteFileModal').getAttribute('data-delete-type');
            if (deleteType === 'file') {
                e.preventDefault(); // Prevent the default form submission
                
                // Hide the modal
                const deleteFileModal = bootstrap.Modal.getInstance(document.getElementById('deleteFileModal'));
                deleteFileModal.hide();
                
                // Proceed with file deletion
                const formData = new FormData();
                formData.append('field_name', fileToDelete.fieldName);

                // Use the correct URL path without the api prefix
                fetch(`/patients/${fileToDelete.modelName}/${fileToDelete.objectId}/delete_file/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showSuccessToast('Το αρχείο διαγράφηκε επιτυχώς!');
                        location.reload();
                    } else {
                        console.error('Error response:', data);
                        showErrorToast('Error deleting file: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showErrorToast('An error occurred while deleting the file.');
                });
                
                // Reset the delete type
                document.getElementById('deleteFileModal').removeAttribute('data-delete-type');
            }
            // If not a file deletion, let the form submit normally
        });
    }
});

function togglePasswordVisibility(inputId, btn) {
    const input = document.getElementById(inputId);
    const icon = btn.querySelector('i');

    if (input.type === "password") {
        input.type = "text";
        icon.classList.remove("bi-eye-fill");
        icon.classList.add("bi-eye-slash-fill");
    } else {
        input.type = "password";
        icon.classList.remove("bi-eye-slash-fill");
        icon.classList.add("bi-eye-fill");
    }
}
function updateSelectField(selectElement) {
    const model = selectElement.dataset.model;
    const id = selectElement.dataset.id;
    const field = selectElement.dataset.field;
    const value = selectElement.value;

    console.log(`🔄 Changing ${field} of model ${model} (id=${id}) to value: ${value}`);

    fetch('/update_field/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({ model, id, field, value })
    })
    .then(res => res.text())  // Use .text() instead of .json() temporarily
    .then(text => {
        console.log('🔍 Raw response:', text);
        try {
            const data = JSON.parse(text);
            console.log('✅ Parsed JSON:', data);
        } catch (err) {
            console.error('❌ Failed to parse JSON:', err);
        }
    })
    .catch(error => console.error('❌ Network error:', error));

}


// Simple email validation for modals
function setupEmailValidation() {
    // Handle modals when they're shown
    document.addEventListener('shown.bs.modal', function(event) {
        const modal = event.target;
        
        // Find email inputs in this modal
        const emailInputs = modal.querySelectorAll('input[type="email"], input[name*="email"], input[id*="email"]');
        
        // Convert text inputs to email type if needed
        emailInputs.forEach(input => {
            if (input.type !== 'email') input.type = 'email';
            
            // Add feedback element if missing
            const parent = input.parentNode;
            if (!parent.querySelector('.invalid-feedback')) {
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = 'Αυτό το email υπάρχει ήδη.';
                parent.appendChild(feedback);
            }
            
            // Add input validation with debounce
            input.addEventListener('input', debounceValidation);
        });
        
        // Set up form submission validation
        const form = modal.querySelector('form');
        if (form && emailInputs.length > 0) {
            form.addEventListener('submit', validateFormSubmission);
        }
    }, true);
    
    // Also set up validation for any email inputs already in the page
    document.querySelectorAll('input[type="email"], input[name*="email"], input[id*="email"]').forEach(input => {
        if (input.type !== 'email') input.type = 'email';
        input.addEventListener('input', debounceValidation);
        
        // Add feedback element if missing
        const parent = input.parentNode;
        if (!parent.querySelector('.invalid-feedback')) {
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'Αυτό το email υπάρχει ήδη.';
            parent.appendChild(feedback);
        }
    });
    
    // Set up form submission validation for all forms
    // document.querySelectorAll('form').forEach(form => {
    //     form.addEventListener('submit', validateFormSubmission);
    // });
}

// Debounced validation function
function debounceValidation() {
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
        const email = this.value.trim();
        if (email.length > 3 && email.includes('@')) {
            validateEmailWithServer(email, this);
        }
    }, 500);
}

// Server validation function
function validateEmailWithServer(email, inputElement) {
    fetch(`/check_email_exists/?email=${encodeURIComponent(email)}`)
        .then(response => response.json())
        .then(data => {
            inputElement.classList.toggle('is-invalid', data.exists);
        })
        .catch(err => {
            console.error("Error checking email:", err);
        });
}

// Form submission validation
function validateFormSubmission(e) {
    const emailInput = this.querySelector('input[type="email"]');
    if (!emailInput) return; // No email input in this form
    
    const email = emailInput.value.trim();
    if (email.length < 3) return; // Allow submission for very short emails (likely empty)
    
    // Skip validation for edit mode
    const idInput = this.querySelector('input[name="id"]');
    if (idInput && idInput.value) return;
    
    // Prevent default to validate first
    e.preventDefault();
    
    // Validate email before submission
    fetch(`/check_email_exists/?email=${encodeURIComponent(email)}`)
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                emailInput.classList.add('is-invalid');
            } else {
                // Email is valid, submit the form
                emailInput.classList.remove('is-invalid');
                this.submit();
            }
        })
        .catch(err => {
            console.error("Error checking email:", err);
            // In case of error, allow submission
            this.submit();
        });
}

// Initialize when DOM is loaded
// document.addEventListener('DOMContentLoaded', setupEmailValidation);

// Toast notification functions
function showSuccessToast(message) {
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toastEl = document.createElement('div');
        toastEl.className = 'toast align-items-center text-white bg-success border-0';
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        toastContainer.appendChild(toastEl);
        
        const toast = new bootstrap.Toast(toastEl);
        toast.show();
        
        // Remove toast after it's hidden
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    } else {
        alert(message);
    }
}

function showErrorToast(message) {
    if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toastEl = document.createElement('div');
        toastEl.className = 'toast align-items-center text-white bg-danger border-0';
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        toastContainer.appendChild(toastEl);
        
        const toast = new bootstrap.Toast(toastEl);
        toast.show();
        
        // Remove toast after it's hidden
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    } else {
        alert(message);
    }
}

function setupTinValidation() {
    document.querySelectorAll('input[name="tin"], input[id*="tin"]').forEach(input => {
        input.type = 'text';  // Ensure it's text input
        input.addEventListener('input', debounceTinValidation);

        // Add feedback element if missing
        const parent = input.parentNode;
        if (!parent.querySelector('.invalid-feedback')) {
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'Αυτό το ΑΦΜ υπάρχει ήδη.';
            parent.appendChild(feedback);
        }
    });
}

function debounceTinValidation() {
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
        const tin = this.value.trim();
        if (tin.length >= 6) {
            validateTinWithServer(tin, this);
        }
    }, 400);
}

function validateTinWithServer(tin, inputElement) {
    fetch(`/check_tin_exists/?tin=${encodeURIComponent(tin)}`)
        .then(response => response.json())
        .then(data => {
            inputElement.classList.toggle('is-invalid', data.exists);
        })
        .catch(err => {
            console.error("Error checking tin:", err);
        });
}

function validateFormSubmission(e) {
    const tinInput = this.querySelector('input[name="tin"]');
    const tin = tinInput?.value?.trim();

    // Skip if editing
    const idInput = this.querySelector('input[name="id"]');
    if (idInput && idInput.value) return;

    if (tin) {
        e.preventDefault();  // Pause submission

        fetch(`/patients/check_tin_exists/?tin=${encodeURIComponent(tin)}`)
            .then(response => response.json())
            .then(data => {
                if (data.exists) {
                    tinInput.classList.add('is-invalid');
                } else {
                    tinInput.classList.remove('is-invalid');
                    this.submit();  // Continue form submission
                }
            })
            .catch(err => {
                console.error("TIN check error:", err);
                this.submit();  // Allow if error in backend
            });
    }
}

function setupAmkaValidation() {
    document.querySelectorAll('input[name="amka"], input[id*="amka"]').forEach(input => {
        input.type = 'text';  // Ensure it's text input
        input.addEventListener('input', debounceAmkaValidation);

        // Add feedback element if missing
        const parent = input.parentNode;
        if (!parent.querySelector('.invalid-feedback')) {
            const feedback = document.createElement('div');
            feedback.className = 'invalid-feedback';
            feedback.textContent = 'Αυτό το ΑΜΚΑ υπάρχει ήδη.';
            parent.appendChild(feedback);
        }
    });
}

function debounceAmkaValidation() {
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
        const amka = this.value.trim();
        if (amka.length >= 5) {
            validateAmkaWithServer(amka, this);
        }
    }, 400);
}

function validateAmkaWithServer(amka, inputElement) {
    const form = inputElement.closest('form');
    const model = form?.querySelector('[name="tablename"]')?.value || '';
    const id = form?.querySelector('[name="id"]')?.value || '';

    fetch(`/check_amka_exists/?amka=${encodeURIComponent(amka)}&model=${model}&id=${id}`)
        .then(response => response.json())
        .then(data => {
            inputElement.classList.toggle('is-invalid', data.exists);
            inputElement.classList.toggle('is-valid', !data.exists && amka.length >= 5);
            
            // Store validation state in a data attribute
            inputElement.dataset.valid = !data.exists;
        })
        .catch(err => {
            console.error("Error checking AMKA:", err);
        });
}

// Add form submission validation
function validateFormSubmission(e) {
    const form = this;
    const amkaInput = form.querySelector('input[name="amka"]');
    
    if (amkaInput && amkaInput.value.trim()) {
        const amka = amkaInput.value.trim();
        
        // Skip validation for edit mode with unchanged AMKA
        const idInput = form.querySelector('input[name="id"]');
        if (idInput && idInput.value && amkaInput.dataset.originalValue === amka) {
            return;
        }
        
        // If we already know it's invalid from real-time validation
        if (amkaInput.classList.contains('is-invalid')) {
            e.preventDefault();
            return;
        }
        
        // Otherwise, check with the server
        e.preventDefault();  // Pause submission
        
        const model = form.querySelector('[name="tablename"]')?.value || '';
        const id = idInput?.value || '';
        
        fetch(`/check_amka_exists/?amka=${encodeURIComponent(amka)}&model=${model}&id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.exists) {
                    amkaInput.classList.add('is-invalid');
                    amkaInput.classList.remove('is-valid');
                } else {
                    amkaInput.classList.remove('is-invalid');
                    amkaInput.classList.add('is-valid');
                    form.submit();  // Continue form submission
                }
            })
            .catch(err => {
                console.error("AMKA check error:", err);
                form.submit();  // Allow if error in backend
            });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setupEmailValidation();
    setupTinValidation();
    setupAmkaValidation();
    
    // Set up form submission validation for all forms
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', validateFormSubmission);
    });
    
    // Store original values for edit forms
    document.addEventListener('shown.bs.modal', function(event) {
        const modal = event.target;
        const amkaInput = modal.querySelector('input[name="amka"]');
        if (amkaInput) {
            amkaInput.dataset.originalValue = amkaInput.value;
        }
    });
});



document.addEventListener('DOMContentLoaded', () => {
    // Add event listeners to filter inputs with debounce
    const filterInputs = document.querySelectorAll('#inline-filter-form input, #inline-filter-form select');
    filterInputs.forEach(input => {
        input.addEventListener('input', debounce(() => {
            document.getElementById('inline-filter-form').submit();
        }, 500));
    });

    // Add change event listeners with proper event handling
    document.querySelectorAll("#inline-filter-form input, #inline-filter-form select").forEach(el => {
        el.addEventListener("change", (event) => {
            // Prevent event propagation to avoid triggering other handlers
            event.stopPropagation();
            
            // Submit the form after a short delay
            setTimeout(() => {
                document.getElementById("inline-filter-form").submit();
            }, 500);
        });
    });

    // Prevent form submission when clicking on action buttons
    document.querySelectorAll('.btn-outline-secondary').forEach(button => {
        button.addEventListener('click', (event) => {
            // Stop the event from bubbling up to the form
            event.stopPropagation();
            // Prevent the default action (which might be form submission)
            event.preventDefault();
            
            // Get the onclick attribute and execute it manually
            const onclickAttr = button.getAttribute('onclick');
            if (onclickAttr) {
                // Execute the onclick function
                eval(onclickAttr);
            }
        });
    });
});

// Debounce function to limit how often a function can be called
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}

function clearFilterInputs() {
    const form = document.getElementById('inline-filter-form');
    if (!form) return;

    const elements = form.querySelectorAll('input, select');
    elements.forEach(el => {
        if (el.type === 'text' || el.type === 'date' || el.tagName.toLowerCase() === 'select') {
            el.value = '';
        }
    });

    form.submit(); // Επαναφορτώνει τη σελίδα με κενά φίλτρα
}

const myOffcanvas = new bootstrap.Offcanvas('#{{ modal_id }}', {
    backdrop: true,
    scroll: true
});
myOffcanvas.show();

