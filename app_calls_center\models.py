from django.db import models
from datetime import date, datetime

class CallsCenter(models.Model):
    administrator_id = models.IntegerField(null=False, blank=False)
    user = models.ForeignKey('app_users.User', on_delete=models.CASCADE, related_name='calls_center')
    date = models.DateField(default=date.today)  # Editable
    time = models.TimeField(default=datetime.now)  # Editable
    general_info = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']  # Default order: latest first
        
    def __str__(self):
        return f"Record for {self.user} on {self.date} {self.time}"
