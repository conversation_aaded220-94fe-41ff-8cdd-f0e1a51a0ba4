ADD_MODAL_ID = 'addModalId'
EDIT_MODAL_ID = 'editModalId'

COLUMNS = [
    {
        'id': 'id',
        'label': '#',
        'field': 'id',
        'type': 'counter',
        'required': True,
        'default_visible': True
    },
    {
        'id': 'first_name',
        'label': 'ΟΝΟΜΑ',
        'field': 'first_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'last_name',
        'label': 'ΕΠΩΝΥΜΟ',
        'field': 'last_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'email',
        'label': 'EMAIL',
        'field': 'email',
        'type': 'text',
        'required': True,
        'default_visible': True
    },
    {
        'id': 'tin',
        'label': 'ΑΦΜ',
        'field': 'tin',
        'type': 'text',
        'required': True,
        'default_visible': True
    },
    {
        'id': 'medical_specialty',
        'label': 'ΕΙΔΙΚΟΤΗΤΑ',
        'field': 'medical_specialty',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'phone',
        'label': 'ΤΗΛΕΦΩΝΟ',
        'field': 'phone',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'address',
        'label': 'ΔΙΕΥΘΥΝΣΗ',
        'field': 'address',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'partnership_agreement',
        'label': 'ΣΥΜΦΩΝΙΑ ΣΥΝΕΡΓΑΣΙΑΣ',
        'field': 'partnership_agreement',
        'type': 'file',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'actions',
        'label': 'ΕΝΕΡΓΕΙΕΣ',
        'type': 'actions',
        'required': True,
        'default_visible': True
    }

]

FILTERS = [
    {
        'id': 'order_by',
        'name': 'order_by',
        'label': 'Ταξινόμηση',
        'type': 'select',
        'width': '150px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
        },
        'options': [
            {'value': 'first_name__asc', 'label': 'Όνομα Αύξουσα ↑'},
            {'value': 'first_name__desc', 'label': 'Όνομα Φθίνουσα ↓'}
        ]
    },
    {
        'id': 'last_name_filter',
        'name': 'last_name__contains',
        'label': 'Επώνυμο',
        'type': 'text',
        'width': '150px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'tin_filter',
        'name': 'tin__contains',
        'label': 'ΑΦΜ',
        'type': 'text',
        'width': '150px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'medical_specialty_filter',
        'name': 'medical_specialty__contains',
        'label': 'Ειδικότητα',
        'type': 'text',
        'width': '150px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    # {
    #     'id': 'phone_filter',
    #     'name': 'phone__contains',
    #     'label': 'Τηλέφωνο',
    #     'type': 'text',
    #     'width': '150px',
    #     'style': {
    #         'container': 'mb-3 me-2',
    #         'label': 'text-secondary fw-bold fs-6',
    #         'input': 'form-control border-secondary'
    #     }
    # },

]

FORM_FIELDS = [
    {
        'id': 'first_name',
        'label': 'Όνομα',
        'type': 'text',
        'required': True,
        'width': '6',
        'placeholder': 'Εισάγετε όνομα'
    },
    {
        'id': 'last_name',
        'label': 'Επώνυμο',
        'type': 'text',
        'required': True,
        'width': '6',
        'placeholder': 'Εισάγετε επώνυμο'
    },
    {
        'id': 'email',
        'label': 'Email',
        'type': 'email',
        'required': True,
        'width': '6',
        'placeholder': 'Εισάγετε email'
    },
    {
        'id': 'tin',
        'label': 'ΑΦΜ',
        'type': 'text',
        'required': True,
        'width': '6',
        'placeholder': 'Εισάγετε ΑΦΜ'
    },
    {
        'id': 'medical_specialty',
        'label': 'Ειδικότητα',
        'type': 'text',
        'required': True,
        'width': '6',
        'placeholder': 'Εισάγετε ειδικότητα'
    },
    {
        'id': 'phone',
        'label': 'Τηλέφωνο',
        'type': 'text',
        'required': True,
        'width': '6',
        'placeholder': 'Εισάγετε τηλέφωνο'
    },
    {
        'id': 'address',
        'label': 'Διεύθυνση',
        'type': 'text',
        'required': True,
        'width': '6',
        'placeholder': 'Εισάγετε διεύθυνση'
    },
    {
        'id': 'partnership_agreement',
        'label': 'Συμφωνία Συνεργασίας',
        'type': 'file',
        'required': False,
        'width': '12',
        'placeholder': 'Επιλέξτε αρχείο'
    }

]


