from django.shortcuts import render

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from utilities.services_app import prepare_filters_and_context, prepare_order, pagination
from app_prescriptions.table_configs.tconf_app_prescriptions import FILTERS, COLUMNS, FORM_FIELDS, ADD_MODAL_ID, EDIT_MODAL_ID
import traceback
from utilities import messages as custom_messages  # ✅ your Messages class
from utilities import parameters as params
from utilities.services_app import createOrUpdate
from utilities.logs_manager import LogsManager
from django.contrib import messages
from django.shortcuts import redirect
from django.shortcuts import get_object_or_404
from django.contrib import messages as django_messages
from utilities import logs_manager, utils
from django.db.models import Case, When, Value, IntegerField
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from utilities.services_app import delete_file_by_url
from app_patients.models import Patient
from app_prescriptions.models import Prescription


logs_manager = LogsManager(params.logs_dir)
messages_obj = custom_messages.Messages()

@login_required
def get_prescriptions(request):
    try:
        prescriptions = Prescription.objects.all()
        prescriptions = prescriptions.filter(administrator_id=request.user.administrator_id)

        query_params = {
            k: v for k, v in request.GET.items()
            if v is not None and v.strip() != '' and v.strip().lower() != 'none'
        }


        filters, context_filters, custom_q_filters = prepare_filters_and_context(query_params, "Prescription")

        order_by_param = request.GET.get('order_by', '')
        if order_by_param:
            ordering = prepare_order(request, order_by_param)
            field, _ = order_by_param.split('__')
            prescriptions = prescriptions.filter(**filters).annotate(
                null_priority=Case(
                    When(**{f"{field}__isnull": True}, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField()
                )
            ).order_by('null_priority', ordering)
        else:
            prescriptions = prescriptions.filter(**filters)
        
        # Get all patients for the current administrator to populate the dropdown
        patients = Patient.objects.filter(administrator_id=request.user.administrator_id)
        
        # Create a copy of the form fields to modify
        form_fields = FORM_FIELDS.copy()
        
        # Find the patient field and populate its options
        for field in form_fields:
            if field['id'] == 'patient':
                field['options'] = [
                    {'value': patient.id, 'label': f"{patient.last_name} {patient.first_name}"}
                    for patient in patients
                ]
                break

        context = {
            'page_obj': pagination(request, prescriptions, 30),
            'order_by': order_by_param,
            **context_filters,
            'filters': FILTERS,
            'columns': COLUMNS,
            'form_fields': form_fields,  # Use the modified form fields
            'addModalId': ADD_MODAL_ID,
            'editModalId': EDIT_MODAL_ID,
            'add_button_label': 'Νέα Συνταγογράφηση',
            'add_modal_title': 'Προσθήκη Συνταγογράφησης',
            'edit_modal_title': 'Επεξεργασία Συνταγογράφησης',
            'modal_description': 'Συμπληρώστε τα παρακάτω στοιχεία για να προσθέσετε μια νέα συνταγογράφηση.',
            'model_name': 'Prescription',
            'page_title': 'Συνταγογραφήσεις',
            'table_id': 'prescriptions_table',
            # 'get_action': 'get_prescriptions',
            'modal_delete_obj_description': '',
            'create_update_url': 'createUpdatePrescription',  # Use the URL name as a string, not the result of reverse()
            'delete_url': 'delete_prescription',  # Use the URL name as a string, not the result of reverse()
        }

        return render(request, 'prescriptions.html', context)

    except Exception as e:
        traceback.print_exc()
        return render(request, 'prescriptions.html', {
            'error_message': f"Σφάλμα: {str(e)}"
        })


def createUpdatePrescription(request):
    try:
        if request.method == 'POST':
            print("REQUEST PATH: " + request.path)
            # Get all form data dynamically
            form_data = {key: value.strip() if isinstance(value, str) else value 
                         for key, value in request.POST.items()}
            
            # Handle file uploads
            for key, file in request.FILES.items():
                form_data[key] = file
            
            # Extract patient ID and convert it to a Patient instance
            patient_id = form_data.pop('patient', None)
            
            # Create or update the prescription
            if 'id' in form_data and form_data['id']:
                # Update existing prescription
                prescription_id = form_data.pop('id')
                prescription = Prescription.objects.get(id=prescription_id)
                
                # Update fields
                for key, value in form_data.items():
                    if key not in ['csrfmiddlewaretoken'] and hasattr(prescription, key):
                        setattr(prescription, key, value)
                
                # Update patient if provided
                if patient_id:
                    try:
                        patient = Patient.objects.get(id=patient_id)
                        prescription.patient = patient
                    except Patient.DoesNotExist:
                        messages.error(request, "Ο ασθενής δεν βρέθηκε!")
                        return redirect('get_prescriptions')
                
                # Ensure administrator_id is set
                prescription.administrator_id = request.user.administrator_id
                
                prescription.save()
                messages.success(request, "Η συνταγογράφηση ενημερώθηκε με επιτυχία!")
            else:
                # Create new prescription
                try:
                    # Get the patient instance
                    if not patient_id:
                        messages.error(request, "Παρακαλώ επιλέξτε ασθενή!")
                        return redirect('get_prescriptions')
                    
                    patient = Patient.objects.get(id=patient_id)
                    
                    # Create new prescription with explicit administrator_id
                    prescription = Prescription(
                        patient=patient,
                        administrator_id=request.user.administrator_id,  # Explicitly set administrator_id
                        barcode_number=form_data.get('barcode_number', ''),
                        prescriber_name=form_data.get('prescriber_name', ''),
                        pharmacy_name=form_data.get('pharmacy_name', '')
                    )
                    prescription.save()
                    messages.success(request, "Η συνταγογράφηση δημιουργήθηκε με επιτυχία!")
                except Patient.DoesNotExist:
                    messages.error(request, "Ο ασθενής δεν βρέθηκε!")
                    return redirect('get_prescriptions')
                except Exception as e:
                    traceback.print_exc()
                    logs_manager.write_error_logs(traceback.format_exc())
                    messages.error(request, f"Σφάλμα: {str(e)}")
                    return redirect('get_prescriptions')
            
            return redirect('get_prescriptions')
        else:
            messages.error(request, "Μη έγκυρη μέθοδος!")
            return redirect('get_prescriptions')

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        messages.error(request, f"Σφάλμα: {str(e)}")
        return redirect('get_prescriptions') 


@login_required
def delete_prescription(request):
    model_class = Prescription
    try:
        if request.method == 'POST':
            obj_id = request.POST.get('obj_id')
            print("delete_prescription with id: " + obj_id)

            if not obj_id:
                django_messages.error(request, "Δεν δόθηκε έγκυρο ID για διαγραφή.")
                return redirect('get_prescriptions')

            obj = get_object_or_404(model_class, id=obj_id)
            print(obj)

            # ✅ Διαγραφή logo αρχείου αν υπάρχει
            # if obj.logo and hasattr(obj.logo, 'url'):
            #     from utils.file_utils import delete_file_by_url
            #     delete_file_by_url(obj.logo.url)

            obj.delete()
            django_messages.success(request, "Το αντικείμενο διαγράφηκε με επιτυχία!")
        else:
            django_messages.error(request, "Μη υποστηριζόμενη μέθοδος.")

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        django_messages.error(request, messages_obj.exception(e, params.default_language))

    return redirect('get_prescriptions') 
