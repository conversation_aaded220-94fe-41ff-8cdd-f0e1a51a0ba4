from django.db import models

class MsgTemplate(models.Model):
    administrator_id = models.IntegerField(null=False, blank=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    type = models.CharField(max_length=256, null=False, blank=False)
    name = models.CharField(max_length=256, null=False, blank=False)
    message = models.TextField(null=False, blank=False)
    class Meta:
        ordering = ['created_at']  # Default ordering

    def __str__(self):
        return f"{self.name}"
    
