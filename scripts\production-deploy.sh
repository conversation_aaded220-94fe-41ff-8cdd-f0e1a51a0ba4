#!/bin/bash
# Full deployment script for pushing to GitLab and updating the production server

# Step 1: Deploy to GitLab
./gitlab-deploy.sh

if [ $? -eq 0 ]; then
    echo "Deployment to GitLab was successful."
else
    echo "Deployment to GitLab failed. Aborting server update."
    exit 1
fi

# Step 2: Ask if the user wants to update the production server
read -p "Do you want to update the production server? (y/n): " confirm_update

if [[ ! $confirm_update =~ ^[Yy]$ ]]; then
    echo "Production server update canceled."
    exit 1
fi

# Step 3: Confirm before pulling latest updates
read -p "Are you sure you want to pull the latest updates on the production server? (y/n): " confirm_pull

if [[ ! $confirm_pull =~ ^[Yy]$ ]]; then
    echo "Pulling latest updates canceled."
    exit 1
fi

# Path of the project directory on the server
SERVER_PATH="/var/www/projects/doctor_home_care_crm"
SERVER_COMMAND="cd $SERVER_PATH && echo 'Inside \$(pwd), pulling latest updates...' && git pull origin main && echo 'Production server updated successfully.'"

# Step 4: Execute the command on the production server
echo "Logging into the production server and pulling latest changes..."
./login-server.sh "$SERVER_COMMAND"

# Step 5: Restart the Django container
echo "Restarting Django container..."
./login-server.sh "docker restart doctor_home_care_crm_django"