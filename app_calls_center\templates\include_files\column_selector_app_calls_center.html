<div class="offcanvas offcanvas-end custom-offcanvas-rounded"
     tabindex="-1"
     id="columnSelectorOffcanvas"
     aria-labelledby="columnSelectorLabel">
    <div class="offcanvas-header px-5 p-4" style="background-color: #6AC7BA">
        <h5 class="offcanvas-title text-white text-center flex-grow-1 fs-4"
            id="columnSelectorLabel">Προσαρμογή Στηλών</h5>
        <button type="button"
                class="btn-close btn-close-white"
                data-bs-dismiss="offcanvas"
                aria-label="Close"></button>
    </div>
    <div class="offcanvas-body px-4 d-flex flex-column">
        <p class="mb-4 mt-3 text-center text-muted small">Επιλέξτε τις στήλες που επιθυμείτε να εμφανίζονται στον πίνακα</p>
        <div class="rounded-4 border w-100 mb-4 overflow-hidden shadow-sm  color-gray"
             style="background: #fff">
            {% for column in columns %}
                {% if not column.required %}
                    <label class="d-flex justify-content-between align-items-center border-bottom px-3 py-3 column-row custom-cursor"
                           style="font-weight: 500">
                        {{ column.label }}
                        <input class="form-check-input column-toggle"
                               type="checkbox"
                               value="{{ column.id }}"
                               {% if column.default_visible %}checked{% endif %}
                               data-table-id="{{ table_id }}">
                    </label>
                {% endif %}
            {% endfor %}
        </div>
        <!-- <button class="btn btn-dark rounded-2 py-2" style="width: 150px; align-self: center" data-bs-dismiss="offcanvas">Αποθήκευση</button> -->
    </div>
</div>
