// Function to toggle column selector menu
function toggleColumnSelector() {
    const menu = document.getElementById('columnSelectorMenu');
    if (menu.style.display === 'none') {
        menu.style.display = 'block';
    } else {
        menu.style.display = 'none';
    }
}

// Close the menu when clicking outside
document.addEventListener('click', function(event) {
    const menu = document.getElementById('columnSelectorMenu');
    const button = document.getElementById('columnSelectorBtn');
    
    if (menu && button && !menu.contains(event.target) && !button.contains(event.target)) {
        menu.style.display = 'none';
    }
});

document.addEventListener('DOMContentLoaded', function() {
    // Get all column toggle checkboxes
    const columnToggles = document.querySelectorAll('.column-toggle');
    console.log('Found column toggles:', columnToggles.length);
    
    // Add event listeners to each checkbox
    columnToggles.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            console.log('Checkbox changed:', this.value, 'Checked:', this.checked);
            
            const tableId = this.dataset.tableId;
            const columnId = this.value;
            const isVisible = this.checked;
            
            // Toggle column visibility
            const columns = document.querySelectorAll(`#${tableId} .column-${columnId}`);
            console.log('Found columns to toggle:', columns.length);
            
            columns.forEach(column => {
                if (isVisible) {
                    column.classList.remove('d-none');
                } else {
                    column.classList.add('d-none');
                }
            });
            
            // Save to localStorage
            const visibleColumns = Array.from(document.querySelectorAll(`.column-toggle[data-table-id="${tableId}"]`))
                .filter(cb => cb.checked)
                .map(cb => cb.value);
            
            localStorage.setItem(`table_${tableId}_columns`, JSON.stringify(visibleColumns));
            console.log('Saved visible columns to localStorage:', visibleColumns);
        });
    });
    
    // Initialize column visibility from localStorage
    document.querySelectorAll('table[id]').forEach(table => {
        const tableId = table.id;
        console.log('Initializing table:', tableId);
        
        const storedColumns = localStorage.getItem(`table_${tableId}_columns`);
        if (storedColumns) {
            const visibleColumns = JSON.parse(storedColumns);
            console.log('Found stored columns:', visibleColumns);
            
            // Get all checkboxes for this table
            const checkboxes = document.querySelectorAll(`.column-toggle[data-table-id="${tableId}"]`);
            
            checkboxes.forEach(checkbox => {
                const columnId = checkbox.value;
                const isVisible = visibleColumns.includes(columnId);
                
                // Update checkbox state
                checkbox.checked = isVisible;
                
                // Update column visibility
                const columns = document.querySelectorAll(`#${tableId} .column-${columnId}`);
                columns.forEach(column => {
                    if (isVisible) {
                        column.classList.remove('d-none');
                    } else {
                        column.classList.add('d-none');
                    }
                });
            });
        }
    });
});