from datetime import datetime, timedelta, timezone
# import platform
import os
import sys

current_dir = os.getcwd()
# sys.path.append(current_dir)
# sys.path.append(f'{current_dir}/coral')
# parent_dir = os.path.dirname(current_dir)
# sys.path.append(parent_dir)
# sys.path.append('/opt/coral/coral')
# sys.path.append('/opt/coral/coral/coral')
from local_settings import k as k
from utilities import crypto as c
from utilities import utils

app_name = 'airos'

latest_app_build = 27
latest_app_version = '2.6(27)'  #1.1(1), ..., 1.9(9), 2.0(10), 2.1(11), 2.2(14) 2.3(24)
url_android_update = 'https://play.google.com/store/apps/details?id=club.betnow.coral&hl=en'#f'https://coral.bet-now.club/coral_{latest_app_version}.apk'
url_ios_update = 'https://apps.apple.com/gr/app/coral/id6569255378'
url_web_coral = 'https://coral.bet-now.club'

default_language = 'el'

token_len = 92  # In case of token_len=92, the encrypted token length is 204
token_expires_in_minutes = 0  # mins
token_expires_in_hours = 0  # hours
token_expires_in_days = 100  # days
tz = timezone.utc
# set_ti_ng_s_k = c.get_decrypted_str_used_in_django_settings()
# Replace with a direct import from settings
from doctor_home_care_crm.settings import FERNET_KEY
set_ti_ng_s_k = FERNET_KEY  # Use the key directly from settings

# firebase_certificate_json = k.e_simvoulos_firebase_adminsdk_tybqj_9457bca0cb

### db
run_localhost = k.run_localhost
# is_on_windows = ('windows' in platform.system().lower())

db_username = k.db_username
db_p = k.db_p
db_name = k.db_name
db_host = k.db_host
db_port = k.db_port
if run_localhost:
    print(f'db_username: {db_name}\ndb_host: {db_host}')

logs_dir = k.logs_dir

# email settings
email_use_tls = k.is_ssl_tls
email_host = k.smtp_server #'smtp.gmail.com'  # 'smtp.server1.codnext.com'
email_user = k.email_username
email_p = k.e_p
email_port = k.smtp_port  # 25

path_to_save_user_files = k.path_to_save_user_files  # for saving diver licenses and insurance contracts


def get_time_token_expired(time_token_created, expires_in_minutes, expires_in_hours, expires_in_days):
    duration_time = timedelta(days=expires_in_days, seconds=0, microseconds=0,
                              milliseconds=0, minutes=expires_in_minutes, hours=expires_in_hours, weeks=0)
    return time_token_created + duration_time


def token_has_expired(time_token_created, expires_in_minutes, expires_in_hours, expires_in_days):
    now = datetime.now(tz=tz)
    return now > get_time_token_expired(time_token_created, expires_in_minutes, expires_in_hours, expires_in_days)


change_password_verification_code_expiration_in_minutes = 10



#pasword validation
password_minimum_length = 8
password_maximum_length = 64
password_has_numbers = True
password_has_letters = True #[a-zA-Z]
password_has_lowercase_letters = False #[a-z]
password_has_uppercase_letters = False #[A-Z]
password_has_special_chars = False #[!@#$%^&*()_+-=[]|:;,.<>?]
password_special_chars = '!@#$%^&*()_+-=[]|:;,.<>?'


# Logs
# if run_localhost:
#     logs_dir = f'{current_dir}/logs'
# else:
#     logs_dir = logs_dir
utils.create_dir_if_not_exists(logs_dir)
currentDay = datetime.now().day
currentMonth = datetime.now().month
if len(f'{currentMonth}') < 2:
    currentMonth = f'0{currentMonth}'
currentYear = datetime.now().year
day_of_creating_log_file = '01'
if currentDay < 16:
    day_of_creating_log_file = '01'
elif currentDay > 15:
    day_of_creating_log_file = '16'
functional_logs_filename = '{}_{}_{}_functional_logs_{}.txt'.format(currentYear, currentMonth,
                                                                    day_of_creating_log_file, app_name)
error_logs_filename = '{}_{}_{}_error_logs_{}.txt'.format(currentYear, currentMonth, '01', app_name)
functional_logs_file_path = f'{logs_dir}/{functional_logs_filename}'
error_logs_file_path = f'{logs_dir}/{error_logs_filename}'



def subject_body_in_sending_validation_code(code, language):
    subject_en = f'XPRICES Verification code: {code}'
    subject_gr = f'XPRICES Κωδικός Επαλήθευσης: {code}'
    body_en = f"""\

The XPRICES API application received a request to send a code to this email.

The code is: {code}

This code will expire in 10 minutes.



Note: If you don’t recognize the XPRICES API, you can safely ignore this email.
"""

    body_gr = f"""\

Η εφαρμογή XPRICES API έλαβε ένα αίτημα για αποστολή κωδικού σε αυτό το e-mail.

Ο κωδικός είναι: {code}

O κωδικός θα λήξει σε 10 λεπτά.



Σημείωση: Εάν δεν αναγνωρίζετε την εφαρμογή XPRICES API, μπορείτε να αγνοήσετε με ασφάλεια αυτό το e-mail.
    """
    subject = utils.text_in_selected_language(subject_gr, subject_en, language)
    body = utils.text_in_selected_language(body_gr, body_en, language)
    return subject, body



new_in_version_html_text = f'''
<b>Οι πληροφορίες εκδόσεων είναι διαθέσιμες μόνο για τον διαχειριστή.</b><br/>
<br/>

<b>XPRICES V1.0 </b> (31/10/2024)<br/>
  - ... <br/>
  <br/>  
  <br/>      
    '''




