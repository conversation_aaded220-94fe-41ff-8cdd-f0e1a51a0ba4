ADD_MODAL_ID = 'addPatientModal'
EDIT_MODAL_ID = 'editPatientModal'

COLUMNS = [
    {
        'id': 'id',
        'label': '#',
        'field': 'id',
        'type': 'counter',
        'required': True,
        'default_visible': True
    },
    {
        'id': 'amka',
        'label': 'ΑΜΚΑ',
        'field': 'amka',
        'type': 'text',
        'required': False,
        'default_visible': True,
    },
    {
        'id': 'last_name',
        'label': 'ΕΠΩΝΥΜΟ',
        'field': 'last_name',
        'type': 'text',
        'required': False,
        'default_visible': True,
    },
    {
        'id': 'first_name',
        'label': 'ΟΝΟΜΑ',
        'field': 'first_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'father_name',
        'label': 'ΠΑΤΡΩΝΥΜΟ',
        'field': 'father_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'date_of_birth',
        'label': 'ΗΜΕΡΟΜΗΝΙΑ ΓΕΝΝΗΣΗΣ',
        'field': 'date_of_birth',
        'type': 'date',
        'required': False,
        'default_visible': True,
    },
    {
        'id': 'address',
        'label': 'ΔΙΕΥΘΥΝΣΗ',
        'field': 'address',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'area',
        'label': 'ΠΕΡΙΟΧΗ',
        'field': 'area',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'postal_code',
        'label': 'Τ.Κ.',
        'field': 'postal_code',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'phone',
        'label': 'ΤΗΛΕΦΩΝΟ ΕΠΙΚΟΙΝΩΝΙΑΣ',
        'field': 'phone',
        'type': 'text',
        'required': False,
        'default_visible': True,
    },
    {
        'id': 'email',
        'label': 'E-MAIL',
        'field': 'email',
        'type': 'text',
        'required': False,
        'default_visible': True,
    },
    {
        'id': 'actions',
        'label': 'ΕΝΕΡΓΕΙΕΣ',
        'type': 'actions',
        'required': True,
        'default_visible': True
    }
]



FORM_FIELDS = [
    {'id': 'first_name', 'label': 'Όνομα', 'type': 'text', 'required': True, 'width': '6', 'placeholder': 'Εισάγετε όνομα'},
    {'id': 'last_name', 'label': 'Επώνυμο', 'type': 'text', 'required': True, 'width': '6', 'placeholder': 'Εισάγετε επώνυμο'},
    {'id': 'amka', 'label': 'ΑΜΚΑ', 'type': 'text', 'required': False, 'width': '6', 'placeholder': 'Εισάγετε ΑΜΚΑ'},
    {'id': 'email', 'label': 'Email', 'type': 'email', 'required': True, 'width': '6'},
    {'id': 'gender', 'label': 'Φύλο', 'type': 'text', 'required': False, 'width': '6', 'placeholder': 'Εισάγετε φύλο'},
    {'id': 'father_name', 'label': 'Όνομα Πατρός', 'type': 'text', 'required': False, 'width': '6', 'placeholder': 'Εισάγετε όνομα πατρός'},
    {'id': 'date_of_birth', 'label': 'Ημερομηνία Γέννησης', 'type': 'date', 'required': False, 'width': '6'},
    {'id': 'recommendation', 'label': 'Σύσταση', 'type': 'select', 'required': False, 'width': '4', 'options': [
        {'value': 'type_a', 'label': 'Τύπος Α'},
        {'value': 'type_b', 'label': 'Τύπος Β'},
    ]},
    {'id': 'contract', 'label': 'Σύμβαση', 'type': 'select', 'required': False, 'width': '4', 'options': [
        {'value': 'private', 'label': 'Ιδιωτική'},
        {'value': 'public', 'label': 'Δημόσια'},
    ]},
    {'id': 'dressing', 'label': 'Επίθεμα', 'type': 'select', 'required': False, 'width': '4', 'options': [
        {'value': 'bandage', 'label': 'Επίδεσμος'},
        {'value': 'gauze', 'label': 'Γάζα'},
    ]},
    {'id': 'address', 'label': 'Διεύθυνση', 'type': 'text', 'required': False, 'width': '6', 'placeholder': 'Εισάγετε διεύθυνση'},
    {'id': 'area', 'label': 'Περιοχή', 'type': 'text', 'required': False, 'width': '6', 'placeholder': 'Εισάγετε περιοχή'},
    {'id': 'floor', 'label': 'Όροφος', 'type': 'text', 'required': False, 'width': '6', 'placeholder': 'Εισάγετε όροφο'},
    {'id': 'bell', 'label': 'Κουδούνι', 'type': 'text', 'required': False, 'width': '6', 'placeholder': 'Εισάγετε κουδούνι'},
    {'id': 'postal_code', 'label': 'ΤΚ', 'type': 'text', 'required': False, 'width': '6', 'placeholder': 'Εισάγετε ΤΚ'},
    {'id': 'city', 'label': 'Πόλη', 'type': 'text', 'required': False, 'width': '6', 'placeholder': 'Εισάγετε πόλη'},
    {'id': 'medical_condition', 'label': 'Ιατρική Συνθήκη', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'phone', 'label': 'Τηλέφωνο', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'icd10_code', 'label': 'ICD10', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'doctor_name', 'label': 'Θεράπων Ιατρός', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'doctor_phone', 'label': 'Τηλέφωνο Θεράποντος Ιατρού', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'doctor_email', 'label': 'Email Θεράποντος Ιατρού', 'type': 'email', 'required': False, 'width': '6'},
    {'id': 'eprescription', 'label': 'Άυλη Συνταγογράφηση', 'type': 'select', 'required': False, 'width': '6', 'options': [
        {'value': 'yes', 'label': 'Ναι'},
        {'value': 'no', 'label': 'Όχι'}
    ]},
    {'id': 'kepa_decision_number', 'label': 'Αριθμός Απόφασης ΚΕΠΑ', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'kepa_decision_date', 'label': 'Ημερομηνία Απόφασης ΚΕΠΑ', 'type': 'date', 'required': False, 'width': '6'},
    {'id': 'insurance_provider', 'label': 'Φορέας Ασφάλισης', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'pharmacy_name', 'label': 'Φαρμακείο Εξυπηρέτησης', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'prescriber_name', 'label': 'Συνταγογραφών', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'prescriber_phone', 'label': 'Τηλέφωνο Συνταγογραφούντος', 'type': 'text', 'required': False, 'width': '6'},
    {'id': 'prescriber_email', 'label': 'Email Συνταγογραφούντος', 'type': 'email', 'required': False, 'width': '6'},
    {'id': 'patient_history', 'label': 'Ιστορικό Ασθενούς', 'type': 'textarea', 'required': False, 'width': '12'},
    {'id': 'financial_offers', 'label': 'Οικονομικές Προσφορές', 'type': 'textarea', 'required': False, 'width': '12'}
]

FILTERS = [
    {
        'id': 'order_by',
        'name': 'order_by',
        'label': 'Ταξινόμηση',
        'field': 'order_by',
        'type': 'select',
        'width': '200px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
        },
        'options': [
            {'value': 'last_name__asc', 'label': 'Επώνυμο Αύξουσα ↑'},
            {'value': 'last_name__desc', 'label': 'Επώνυμο Φθίνουσα ↓'},
            {'value': 'date_of_birth__asc', 'label': 'Ημ. Γέννησης Αύξουσα ↑'},
            {'value': 'date_of_birth__desc', 'label': 'Ημ. Γέννησης Φθίνουσα ↓'},
        ]
    },
    {
        'id': 'amka_filter',
        'name': 'amka__contains',
        'label': 'ΑΜΚΑ',
        'field': 'amka',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'last_name_filter',
        'name': 'last_name__contains',
        'label': 'Επώνυμο',
        'field': 'last_name',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'first_name_filter',
        'name': 'first_name__contains',
        'label': 'Όνομα',
        'field': 'first_name',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'father_name_filter',
        'name': 'father_name__contains',
        'label': 'Πατρώνυμο',
        'field': 'father_name',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'date_of_birth_filter',
        'name': 'date_of_birth',
        'label': 'Ημ. Γέννησης',
        'field': 'date_of_birth',
        'type': 'date',
        'width': '50px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'address_filter',
        'name': 'address__contains',
        'label': 'Διεύθυνση',
        'field': 'address',
        'type': 'text',
        'width': '200px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'area_filter',
        'name': 'area__contains',
        'label': 'Περιοχή',
        'field': 'area',
        'type': 'text',
        'width': '200px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'postal_code_filter',
        'name': 'postal_code__contains',
        'label': 'ΤΚ',
        'field': 'postal_code',
        'type': 'text',
        'width': '120px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'phone_filter',
        'name': 'phone__contains',
        'label': 'Τηλέφωνο',
        'field': 'phone',
        'type': 'text',
        'width': '160px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'email_filter',
        'name': 'email__contains',
        'label': 'Email',
        'field': 'email',
        'type': 'text',
        'width': '200px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    }
]






































