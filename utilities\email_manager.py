import smtplib
import ssl
import os
import sys
import traceback
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

current_dir = os.getcwd()
sys.path.append(current_dir)
# sys.path.append(f'{current_dir}/coral')
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
# sys.path.append('/opt/coral')
# sys.path.append('/opt/coral/coral')
# sys.path.append('/opt/coral/coral/coral')


# from utilities import k
from utilities import parameters as params
from utilities import utils
from utilities.logs_manager import LogsManager


logs_manager_obj = LogsManager(params.logs_dir)


class Email_manage:
    # EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_USE_TLS = params.email_use_tls  # True
    EMAIL_HOST = params.email_host  # 'server1.codnext.com' #'smtp.gmail.com'
    EMAIL_HOST_USER = params.email_user
    EMAIL_HOST_PASSWORD = params.email_p
    EMAIL_PORT = params.email_port  # 465


    text = """\
    Hi, {}
    How are you?
    Real Python has many great tutorials:
    www.realpython.com""".format(256)
    html = """\
    <html>
      <body>
        <p>Hi,<br>
           How are you?<br>
           <a href="">Real Python</a> 
           has many great tutorials.
        </p>
      </body>
    </html>
    """

    def send_mail(self, email_from, email_to, subject, body_text='', body_html='', language='en'):
        try:
            port = self.EMAIL_PORT
            smtp_server = self.EMAIL_HOST
            sender_email = self.EMAIL_HOST_USER
            password = self.EMAIL_HOST_PASSWORD
            receiver_email = email_to

            # print(port)
            # print(smtp_server)
            # print(sender_email)
            # print(password)
            # print(receiver_email)

            # logs.write_error_logs(f'port: {port}')
            # logs.write_error_logs(smtp_server)
            # logs.write_error_logs(sender_email)
            # logs.write_error_logs(password)
            # logs.write_error_logs(receiver_email)
            # logs.write_error_logs(self.EMAIL_USE_TLS)

            message = MIMEMultipart()
            # message = MIMEMultipart('alternative')
            message['Subject'] = subject
            message['From'] = email_from
            message['To'] = email_to

            # body_t = body_text.encode('utf-8')
            # message.attach(MIMEText(body_t, 'plain', 'utf-8'))

            body_h = body_html.encode('utf-8')
            message.attach(MIMEText(body_h, 'html', 'utf-8'))

            body_t = body_text.encode('utf-8')
            message.attach(MIMEText(body_t, 'plain', 'utf-8'))

            # context = ssl.create_default_context()
            if self.EMAIL_USE_TLS:
                logs_manager_obj.write_error_logs('use TLS')
                with smtplib.SMTP_SSL(smtp_server, port) as server:
                    server.ehlo()
                    server.login(sender_email, password)
                    server.sendmail(sender_email, receiver_email, message.as_string())
            else:
                logs_manager_obj.write_error_logs('Not use TLS')
                with smtplib.SMTP(smtp_server, port) as server:
                    server.ehlo()
                    server.login(sender_email, password)
                    server.sendmail(sender_email, receiver_email, message.as_string())
            # smtp_server.close()
            # s.sendmail(me, you, msg.as_string())
            return True, 'Success'
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            m_en = f'The service of sending emails is temporarily unable to handle the request (error: {e}).'
            m_gr = f'Η υπηρεσία αποστολής email δεν είναι προσωρινά σε θέση να χειριστεί το αίτημα σας (σφάλμα: {e}).'
            return False, utils.text_in_selected_language(m_gr, m_en, language)

    def send_mail_with_gmail(self, email_from, email_to, subject, body_text='', body_html='', language='en'):
        try:
            port = self.EMAIL_PORT
            smtp_server = self.EMAIL_HOST
            sender_email = self.EMAIL_HOST_USER
            password = self.EMAIL_HOST_PASSWORD
            receiver_email = email_to
            # body = message
            # message = str.encode('Subject: {}\n\n{}'.format(subject, body), 'utf-8')
            # Create message container - the correct MIME type is multipart/alternative.
            message = MIMEMultipart()
            # message = MIMEMultipart('alternative')
            message['Subject'] = subject
            message['From'] = email_from
            message['To'] = email_to

            body_h = body_html.encode('utf-8')
            message.attach(MIMEText(body_h, 'html', 'utf-8'))

            body_t = body_text.encode('utf-8')
            message.attach(MIMEText(body_t, 'plain', 'utf-8'))

            context = ssl.create_default_context()
            with smtplib.SMTP(smtp_server, port) as server:
                # server.ehlo()  # Can be omitted
                server.starttls(context=context)
                # server.ehlo()  # Can be omitted
                server.login(sender_email, password)
                server.sendmail(sender_email, receiver_email, message.as_string())
                # s.sendmail(me, you, msg.as_string())
            return True, 'Success'
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            m_en = f'The service of sending emails is temporarily unable to handle the request (error: {e}).'
            m_gr = f'Η υπηρεσία αποστολής email δεν είναι προσωρινά σε θέση να χειριστεί το αίτημα σας (σφάλμα: {e}).'
            return False, utils.text_in_selected_language(m_gr, m_en, language)




    def send_mail_old(self, email_from, email_to, subject, body):
        try:
            port = self.EMAIL_PORT
            smtp_server = self.EMAIL_HOST
            sender_email = self.EMAIL_HOST_USER
            password = self.EMAIL_HOST_PASSWORD
            receiver_email = email_to
            subject = subject
            # body = message
            message = str.encode('Subject: {}\n\n{}'.format(subject, body))
            context = ssl.create_default_context()
            with smtplib.SMTP(smtp_server, port) as server:
                server.ehlo()  # Can be omitted
                server.starttls(context=context)
                server.ehlo()  # Can be omitted
                server.login(sender_email, password)
                server.sendmail(sender_email, receiver_email, message)
            return True, 'Success'
        except Exception as e:
            utils.exception_traceback_print()
            m = 'The service of sending emails is temporarily unable to handle the request'
            m_gr = 'Η υπηρεσία αποστολής email δεν είναι προσωρινά σε θέση να χειριστεί το αίτημα σας'
            return False, f'{m_gr} (σφάλμα: {e})'

# a = Email()
# a.send_mail(email_from='<EMAIL>', email_to='<EMAIL>', subject='Account activation',  body=a.text)
# send_mail(
#      "Subject here",
#      "Here is the message.",
#      '<EMAIL>',
#      ['<EMAIL>'],
#      fail_silently=False,
#  )
