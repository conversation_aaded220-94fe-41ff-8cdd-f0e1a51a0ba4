// document.addEventListener('DOMContentLoaded', function () {
//     // Initialize Offcanvas instances
//     document.querySelectorAll('.offcanvas').forEach(el => {
//         bootstrap.Offcanvas.getOrCreateInstance(el);
//     });

//     // Handle column toggle changes
//     document.querySelectorAll('.column-toggle').forEach(checkbox => {
//         checkbox.addEventListener('change', function () {
//             const tableId = this.dataset.tableId;
//             const columnId = this.value;
//             const isVisible = this.checked;

//             // Toggle visibility of columns
//             document.querySelectorAll(`#${tableId} .column-${columnId}`).forEach(col => {
//                 col.classList.toggle('d-none', !isVisible);
//             });
//             document.querySelectorAll(`#${tableId} col.column-${columnId}`).forEach(col => {
//                 col.classList.toggle('d-none', !isVisible);
//             });

//             // Save visible columns to localStorage
//             const visibleColumns = Array.from(document.querySelectorAll(`.column-toggle[data-table-id="${tableId}"]`))
//                 .filter(cb => cb.checked)
//                 .map(cb => cb.value);
//             localStorage.setItem(`table_${tableId}_columns`, JSON.stringify(visibleColumns));
//         });
//     });

//     // On page load: restore saved column visibility
//     document.querySelectorAll('table[id]').forEach(table => {
//         const tableId = table.id;
//         const stored = localStorage.getItem(`table_${tableId}_columns`);
//         if (stored) {
//             const visibleColumns = JSON.parse(stored);
//             document.querySelectorAll(`.column-toggle[data-table-id="${tableId}"]`).forEach(cb => {
//                 const columnId = cb.value;
//                 const isVisible = visibleColumns.includes(columnId);
//                 cb.checked = isVisible;

//                 document.querySelectorAll(`#${tableId} .column-${columnId}`).forEach(col => {
//                     col.classList.toggle('d-none', !isVisible);
//                 });
//                 document.querySelectorAll(`#${tableId} col.column-${columnId}`).forEach(col => {
//                     col.classList.toggle('d-none', !isVisible);
//                 });
//             });
//         }
//     });
// });
