import os
import re
import glob
import datetime
import shutil
import traceback
import pickle
import random
import base64
import time
import random
from pytz import timezone
import ast
import math
from django.core.serializers import serialize
import json
import xlrd
import utilities
from utilities.logs_manager import LogsManager
# from utilities import logs_manager
from local_settings import k
from io import BytesIO
import base64
import barcode
from barcode.writer import ImageWriter

logs_manager_obj = LogsManager(k.logs_dir)


def password_validator(password, excluded_current_password='', min_len=8, max_len=64, numbers=True, letters=True,
lowercase_letters=False,
                       uppercase_letters=False,  has_special_chars=False, special_chars='!@#$%^&*()_+-=[]|:;,.<>?',
                       language='en'):
    m_el = ''
    m_en = ''
    # print('password: ', password)
    if password == excluded_current_password:
        m_el = f"Ο κωδικός πρέπει να είναι διαφορετικός από τον τρέχον. "
        m_en = f"The new password must be different from the current one."
        return False, text_in_selected_language(m_el, m_en, language)
    if len(f'{password}') < min_len:
        m_el = f"Ο κωδικός πρέπει να αποτελείται από τουλάχιστον {min_len} χαρακτήρες. "
        m_en = f"Password must be at least {min_len} characters long."
        return False, text_in_selected_language(m_el, m_en, language)
    if len(f'{password}') > max_len:
        m_el = f"Ο κωδικός πρέπει να περιλαμβάνει το πολύ {max_len} χαρακτήρες. "
        m_en = f"Password must contain at most {max_len} characters."
        return False, text_in_selected_language(m_el, m_en, language)
    is_password_valid = True
    if numbers:
        # Password must contain at least one digit
        if not re.search(r"[0-9]", password):
            is_password_valid = False
    if letters:
        # Password must contain at least one lowercase letter
        if not re.search(r"[A-Za-z]", password):
            is_password_valid = False
    if lowercase_letters:
        # Password must contain at least one lowercase letter
        if not re.search(r"[a-z]", password):
            is_password_valid = False
    if uppercase_letters:
        # Password must contain at least one uppercase letter
        if not re.search(r"[A-Z]", password):
            is_password_valid = False
    if has_special_chars:
        special_chars = r'' + f'{special_chars}'
        # Password must contain at least one special character (e.g., !@#$%^&*)
        if not re.search(f'[{re.escape(special_chars)}]', password):
            is_password_valid = False
    if is_password_valid:
        return True, ''
    else:
        flag = False
        m_el = "Ο κωδικός πρέπει να περιλαμβάνει"
        m_en = "Password must contain"
        if numbers and not letters or numbers and (lowercase_letters or uppercase_letters):
            m_el += " αριθμητικά ψηφία"
            m_en += " numerical digits"
            flag = True
        elif numbers and letters:
            m_el += " αριθμητικά ψηφία και λατινικά γράμματα"
            m_en += " numerical digits and letters"
            flag = True
        if letters and not numbers and (not lowercase_letters or not uppercase_letters):
            if flag:
                m_el += ","
                m_en += ","
            m_el += " λατινικά γράμματα"
            m_en += " letters"
            flag = True
        if lowercase_letters:
            if flag:
                m_el += ","
                m_en += ","
            m_el += " λατινικά πεζά γράμματα"
            m_en += " lowercase letters"
            flag = True
        if uppercase_letters:
            if flag:
                m_el += ","
                m_en += ","
            m_el += " λατινικά κεφαλαία γράμματα"
            m_en += " uppercase letters"
            flag = True
        if has_special_chars:
            if flag:
                m_el += " και"
                m_en += " and"
            m_el += f" τουλάχιστον ένα από τα σύμβολα {special_chars} "
            m_en += f" at least one of the symbols {special_chars} "
    return is_password_valid, text_in_selected_language(m_el, m_en, language)



def text_in_selected_language(el_str, en_str, language):
    if language == 'el':
        return el_str
    else:
        return en_str


def is_time_difference_greater(created_at_datetime, minutes=0, hours=0, days=0):
    """
    Compares if the current datetime - created_at_datetime is greater than the sum of days+minutes+hours.

    Args:
    created_at_datetime (datetime): The datetime when the object was created.
    minutes (int): Number of minutes to add to the duration.
    hours (int): Number of hours to add to the duration.
    days (int): Number of days to add to the duration.

    Returns:
    bool: True if the current datetime - created_at_datetime is greater than the specified duration, False otherwise.
    """
    current_datetime = datetime.datetime.now()
    duration = datetime.timedelta(days=days, hours=hours, minutes=minutes)
    return (current_datetime - created_at_datetime) > duration


def is_time_difference_greater_2(datetime1, datetime2, minutes=0, hours=0, days=0, seconds=0):
    """
    Compares if the current datetime - created_at_datetime is greater than the sum of days+minutes+hours.

    Args:
    created_at_datetime (datetime): The datetime when the object was created.
    minutes (int): Number of minutes to add to the duration.
    hours (int): Number of hours to add to the duration.
    days (int): Number of days to add to the duration.

    Returns:
    bool: True if the current datetime - created_at_datetime is greater than the specified duration, False otherwise.
    """
    duration = datetime.timedelta(days=days, hours=hours, minutes=minutes, seconds=seconds)
    return (datetime2 - datetime1) > duration


def get_object_json_serializable(object):
    try:
        if object:
            # print(user)
            obj_str_json = serialize('json', object)
            obj_json = json.loads(obj_str_json)
            
            # Process ImageFields for each object in the queryset
            for i, item in enumerate(obj_json):
                model_instance = object[i]
                for field in model_instance._meta.fields:
                    if isinstance(field, models.ImageField) and field.name in item['fields']:
                        image_field = getattr(model_instance, field.name)
                        if image_field:
                            item['fields'][field.name] = image_field.url
            
            return obj_json
        else:
            return None
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return None


def get_user_json(user):
    try:
        user.password = None
        user_str_json = serialize('json', [user])[1:-1]
        user_json = json.loads(user_str_json)
        return user_json
        # print('user_json ', user_json)
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return None


def decode_file_base64_str_to_file_saving_to_path(file_base64_str, file_path):
    try:
        file_content = base64.b64decode(file_base64_str)
        with open(file_path, 'wb') as file:
            file.write(file_content)
            return True, 'success'
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return False, f'{e}'


def get_current_date_time_str(format_date_time_str="%Y-%m-%d %H:%M:%S"):
    now = datetime.datetime.now()
    return now.strftime(format_date_time_str)


def convert_list_to_list_str(list_object):
    try:
        list_str = ', '.join(map(str, list_object))
        return f'[{list_str}]'
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return ''


def convert_list_str_to_list(list_str):
    try:
        list_object = ast.literal_eval(list_str)
        return list_object
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return []


def pagination(entry_list, page=1, entries_per_page=20, number_of_entries=None):
    entry_no_list = []
    # number_of_entries = 0
    number_of_pages = 0
    pages_list = []
    try:
        first_instance_index = (page - 1) * entries_per_page
        last_instance_index = page * entries_per_page
        # print(first_instance_index, last_instance_index, number_of_entries)
        # print('26', entry_list)
        if not number_of_entries:
            number_of_entries = len(entry_list)
        entry_no_list = []
        n = first_instance_index
        indx = 0
        for k in entry_list:

            indx += 1
            if indx > last_instance_index:
                break
            if indx > first_instance_index:
                n += 1
                entry_no_list.append((k, n))
        number_of_pages = math.ceil(number_of_entries / entries_per_page)
        pages_list = []
        for i in range(1, number_of_pages + 1):
            pages_list.append(i)
        return entry_no_list, number_of_entries, number_of_pages, pages_list
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return entry_no_list, number_of_entries, number_of_pages, pages_list


def create_token(token_len):
    s = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!"#$%&()*+,-./:;<=>?@[]^_|~0123456789'
    len_of_s = len(s)
    token = ''
    for i in range(0, token_len):
        pos = random.randint(0, len_of_s - 1)
        token += s[pos]
    # print(f'{len(token)} {token}')
    return token


# import random
# print(create_token(92))

def get_seed():
    return abs(int(time.time() * 109) - 185874000000)


def get_pdf_filenames_from_dir(dir_path):
    pdf_paths_list = []
    if check_dir_existence(dir_path):
        os.chdir(dir_path)
        for path in glob.glob("*.pdf"):
            # print(path)
            pdf_paths_list.append(path)
    return pdf_paths_list


def get_jpeg_filepaths_from_dir(dir_path):
    os.chdir(dir_path)
    pdf_paths_list = []
    for path in glob.glob("*.jpg"):
        # print(path)
        pdf_paths_list.append(path)
    return pdf_paths_list


def create_dir_if_not_exists(dir_path):
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)


def remove_a_file(file_path):
    os.remove(file_path)


def remove_all_files_of_a_dir(dir_path):
    files = glob.glob('{}/*'.format(dir_path))
    for f in files:
        os.remove(f)


def remove_specific_files_of_a_dir(filepaths_list):
    for f in filepaths_list:
        os.remove(f)


def print_for_debugging(t, debug):
    if debug:
        print(t, end='')


def open_or_create_a_file(filepath, opening_mode, add_blank_space_in_first_line=False):
    try:
        if check_file_existence(filepath):
            file = open(filepath, opening_mode, encoding='utf8')
            return file
        else:
            file = open(filepath, 'w+', encoding='utf8')
            if add_blank_space_in_first_line:
                file.write(' ')
            file.close()
            file = open(filepath, opening_mode, encoding='utf8')
            return file
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return None


def check_file_existence(filepath):
    return os.path.isfile(filepath)


def check_dir_existence(dir):
    return os.path.isdir(dir)


def convert_dd_mm_yy_date_to_datetime_Y_m_d(date):
    # import datetime
    date_list = date.split('-')
    if len(date_list[2]) == 2:
        date = f'{date_list[0]}-{date_list[1]}-20{date_list[2]}'
    d = datetime.datetime.strptime(date, "%d-%m-%Y").strftime("%Y-%m-%d")
    return d


def exception_traceback_print():
    traceback.print_exc()


def exception_traceback_str():
    return traceback.format_exc()


def copy_file_from_dir_to_dir(src_filepath, dst_dir):
    try:
        shutil.copy(src_filepath, dst_dir)
        return f'Copy file: {src_filepath} to: {dst_dir}', True
    except Exception:
        print(traceback.format_exc())
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return f'Error: Failed to copy file: {src_filepath} to: {dst_dir}', False


def rename_a_file(src_file_path, new_named_file_path):
    # import os
    os.rename(src_file_path, new_named_file_path)


def get_current_datetime(tz='utc'):
    # tz = 'Europe/Athens'
    # tz = 'utc'
    try:
        return str(datetime.datetime.now(timezone(tz)))
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return str(datetime.datetime.now())


def move_file_from_dir_to_dir(src_filepath, dst_dir, rename_if_file_exists=False, renaming_suffix='_copy'):
    try:
        filename = os.path.basename(src_filepath)
        file_exists = check_file_existence(f'{dst_dir}/{filename}')
        if file_exists:
            if rename_if_file_exists:
                new_named_file_path = src_filepath + renaming_suffix
                rename_a_file(src_filepath, new_named_file_path)
                shutil.move(new_named_file_path, dst_dir)
                return f'File {filename} is RENAMED to {os.path.basename(new_named_file_path)} because of its existence to destination directory. The renamed file MOVED from: {new_named_file_path} to: {dst_dir}.'
                # shutil.move(src_filepath + renaming_suffix, dst_dir)
            else:
                return f'Failed to move file: {src_filepath} to: {dst_dir} because of its existence to destination directory'
        shutil.move(src_filepath, dst_dir)
        return f'Move file: {src_filepath} to: {dst_dir}'
    except Exception as e:
        # m = exception_traceback_str(e)
        print(traceback.format_exc())
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return f'Error: Failed to move file: {src_filepath} to: {dst_dir}. {e}'


def browse_folder(path):
    path = os.path.realpath(path)
    os.startfile(path)


def string_contains_substring(string, substring, case_sensitive=True) -> bool:
    if case_sensitive:
        substring = substring.lower()
        string = string.lower()
    if substring in string:
        return True
    return False


def read_pickle_file(path_to_pickle_file):
    with open(path_to_pickle_file, "rb") as f:
        b = pickle.load(f)
        return b


# It create a binary file of a given data_structure (list, dict, etc.)
def write_pickle_file(path_to_pickle_file, data_structure):
    with open(path_to_pickle_file, 'wb') as f:
        pickle.dump(data_structure, f)


def sort_by_second(input_list_of_tuples, descending=True):
    if descending:
        return sorted(input_list_of_tuples, key=lambda tup: -tup[1])
    else:
        return sorted(input_list_of_tuples, key=lambda tup: tup[1])


def sort_by_third(input_list_of_tuples, descending=True):
    if descending:
        return sorted(input_list_of_tuples, key=lambda tup: -tup[2])
    else:
        return sorted(input_list_of_tuples, key=lambda tup: tup[2])


def sort_by_fourth(input_list_of_tuples, descending=True):
    if descending:
        return sorted(input_list_of_tuples, key=lambda tup: -tup[3])
    else:
        return sorted(input_list_of_tuples, key=lambda tup: tup[3])


def sort_by_nth(input_list_of_tuples, sort_by_n_th, descending=True):
    n_th = sort_by_n_th - 1
    if descending:
        return sorted(input_list_of_tuples, key=lambda tup: -tup[n_th])
    else:
        return sorted(input_list_of_tuples, key=lambda tup: tup[n_th])


from operator import itemgetter


def sort_list_of_string_tuples_by_fist_and_second_element(list_of_string_tuples, reverse_first=False,
                                                          reverse_second=True):
    list_of_string_tuples.sort(key=itemgetter(1), reverse=reverse_second)
    list_of_string_tuples.sort(key=itemgetter(0), reverse=reverse_first)
    return list_of_string_tuples


def sort_list_of_string(list_of_string, reverse=False):
    list_of_string.sort(reverse=reverse)
    return list_of_string


def append_text_to_file(file_path, text):
    f = open(file_path, 'a', encoding='utf8')
    f.write(text)
    f.close()


def create_dir_if_not_exists(dir_path):
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)


def remove_a_file(file_path):
    os.remove(file_path)


# a = [('a', 5), ('b', 4), ('c', 10), ('d', 4), ('e', 5)]
# a = sort_by_second(a)
# print(a)

# a = {}
# a['a'] = 2
# a['b'] = 4
# a['c'] = 5
# a['c'] = 6
# a['a'] = 6

def create_pickle_file_if_not_exists(path_to_pickle_file, data_structure_for_initialization):
    try:
        c = check_file_existence(path_to_pickle_file)
        if c:
            return True
        else:
            write_pickle_file(path_to_pickle_file, data_structure_for_initialization)
            return True
    except Exception as e:
        logs_manager_obj.write_error_logs(traceback.format_exc())
        traceback.print_exc()
        return True


# print(a)


def print_lines_of_file(file_path, from_line, to_line):
    file = open(file_path, 'r', encoding='utf8')
    line_counter = 0
    for line in file:
        line_counter += 1
        if line_counter > from_line - 1 and line_counter < to_line + 1:
            print('Line {}:{}'.format(line_counter, line), end='')


def get_filename_from_path(path):
    return os.path.basename(path)







class ReadExcel:
    def read_excel_worksheet(self, xls_file_path, sheet_name=None, sheet_index=None):
        worksheet = None
        workbook = xlrd.open_workbook(xls_file_path)
        if sheet_name:
            worksheet = workbook.sheet_by_name(sheet_name)
        elif sheet_index:
            worksheet = workbook.sheet_by_index(sheet_index)
        else:
            print(
                f'Please set sheet_name or sheet_index because sheet_name={sheet_name} and sheet_index={sheet_index}.')
        return workbook, worksheet

    def read_excel_cell(self, worksheet, row, column):
        return worksheet.cell(row, column).value

    def read_headers_index_of_worksheet(self, header_row=1, num_of_columns=25):
        koinontita_dimos_set = set()
        workbook, worksheet = self.read_excel_worksheet(
            xls_file_path='D:/GoogleDrivePNG/Codnext-shared/Dromologitis/kallikraths_kwdikologio1_31_5_11.xls',
            sheet_index=1)
        index_header_list = []
        row = header_row
        index = 0
        for col in range(0, num_of_columns):
            value = self.read_excel_cell(worksheet, row, col)
            index_header_list.append((index, value))
            index += 1
        for (i, h) in index_header_list:
            print(f'(\'{i}\', \'{h}\'),')

    def read_periferia_dimos_form_excel(self):
        koinontita_dimos_set = set()
        workbook, worksheet = self.read_excel_worksheet(
            xls_file_path='D:/GoogleDrivePNG/Codnext-shared/Dromologitis/kallikraths_kwdikologio1_31_5_11.xls',
            sheet_index=1)
        for row in range(2, 6163):
            t = ()
            for col in [12, 6]:
                value = self.read_excel_cell(worksheet, row, col)
                t = t + (value,)
            # print(t)
            koinontita_dimos_set.add(t)
        koinontita_dimos_list = []
        for (k, d) in koinontita_dimos_set:
            koinontita_dimos_list.append((k, d))
        koinontita_dimos_list = sort_list_of_string_tuples_by_fist_and_second_element(koinontita_dimos_list)
        for (k, d) in koinontita_dimos_list:
            print(f'(\'{k}\', \'{d}\'),')

    def read_header_data_form_excel(self, column_index=12):
        column_value_set = set()
        column_value_list = []
        workbook, worksheet = self.read_excel_worksheet(
            xls_file_path='D:/GoogleDrivePNG/Codnext-shared/Dromologitis/kallikraths_kwdikologio1_31_5_11.xls',
            sheet_index=1)
        for row in range(2, 6163):
            value = self.read_excel_cell(worksheet, row, column_index)
            column_value_set.add(value)
        for v in column_value_set:
            column_value_list.append(v)
        column_value_list = sort_list_of_string(column_value_list, reverse=False)
        for v in column_value_list:
            print(f'\'{v}\',')
        print(len(column_value_list))


def random_numerical_code(integer_from=0, integer_to=9, code_length=6):
    code = ''
    for i in range(0, code_length):
        r = random.randint(integer_from, integer_to)
        code = f'{code}{r}'
    # print(f'code {code}')
    return f'{code}'




def file_to_base64_str(file_path):
    try:
        with open(file_path, "rb") as file:
            encoded_string = base64.b64encode(file.read())
            return encoded_string.decode("utf-8")
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return None


def base64_str_to_file_saving_to_path(self, file_base64_str, file_path):
    try:
        file_content = base64.b64decode(file_base64_str)
        with open(file_path, 'wb') as file:
            file.write(file_content)
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return None


def show_tree_structure_of_files(startpath='D:/Projects/codnext_workplace/dromologitis_django',
                                 folder_excluded_list=['.idea', '__pycache__', '.git', 'Lib', 'Scripts']):
    for root, dirs, files in os.walk(startpath):
        # print(f'root: {root}\n\tdirs: {dirs}\n\t\tfiles: {files}')
        flag = True
        for i in folder_excluded_list:
            if i in root:
                flag = False
        if flag:
            level = root.replace(startpath, '').count(os.sep)
            indent = ' ' * 4 * (level)
            print('{}{}/'.format(indent, os.path.basename(root)))
            subindent = ' ' * 4 * (level + 1)
            for f in files:
                print('{}{}'.format(subindent, f))


# show_tree_structure_of_files()


def jocker():
    import random
    r_list = []
    j = random.randint(1, 20)
    for i in range(0, 50):
        r = random.randint(1, 49)
        if r not in r_list:
            r_list.append(r)
        if len(r_list) == 5:
            break
    print(f'Jocker: {j} \nΠεντάδα: {r_list[0]} {r_list[1]} {r_list[2]} {r_list[3]} {r_list[4]}')


def generate_barcode_base64(text: str) -> str:
    from io import BytesIO
    import base64
    import barcode
    from barcode.writer import ImageWriter

    buffer = BytesIO()
    code128 = barcode.get('code128', text, writer=ImageWriter())
    code128.write(buffer, {
        # 'module_height': 10.0,
        'font_size': 10,            # 🔽 Smaller font for text under barcode     # Tighter space between barcode and text
        'module_width': 0.4        # Optional: adjust bar width for compactness
    })
    encoded = base64.b64encode(buffer.getvalue()).decode('utf-8')
    return encoded
