from django.urls import path, re_path
from . import views
from django.conf import settings
from django.conf.urls.static import static



urlpatterns = [
    path('', views.get_partners, name='get_partners'),
    path('createUpdatePartner/', views.createUpdatePartner, name='createUpdatePartner'),
    path('delete_partner/', views.delete_partner, name='delete_partner'),
    # Add multiple patterns to catch different URL formats
    path('<str:model_name>/<int:object_id>/delete_file/', views.delete_file, name='delete_file'),
    path('<str:model_name>/<int:object_id>/upload_file/', views.upload_file, name='upload_file'),

] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
