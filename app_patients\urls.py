from django.urls import path
from .views import patients_views as patient_views
from .views import patient_detail_views as patient_detail_views
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('', patient_views.get_patients, name='get_patients'),
    path('createUpdatePatient/', patient_views.createUpdatePatient, name='createUpdatePatient'),
    path('delete_patient/', patient_views.delete_patient, name='delete_patient'),

    path('patients/<int:patient_id>/tab/<str:tab>/', patient_detail_views.patient_tab_content, name='patient_tab_content'),
    # path('patients/<int:patient_id>/update/', patient_detail_views.update_patient, name='update_patient'),
    path('patients/<int:patient_id>/tab/medical_notes/delete/<int:note_id>/', patient_detail_views.delete_medical_note, name='delete_medical_note'),
    path(
        'patients/<int:patient_id>/tab/medical_notes/update/<int:note_id>/',
        patient_detail_views.update_medical_note,
        name='update_medical_note'
    ),


] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
