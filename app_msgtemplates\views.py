from django.shortcuts import render

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from utilities.services_app import prepare_filters_and_context, prepare_order, pagination
from app_msgtemplates.table_configs.tconf_app_msgtemplates import FILTERS, COLUMNS, FORM_FIELDS, ADD_MODAL_ID, EDIT_MODAL_ID
import traceback
from utilities import messages as custom_messages  # ✅ your Messages class
from utilities import parameters as params
from utilities.services_app import createOrUpdate
from utilities.logs_manager import LogsManager
from django.contrib import messages
from django.shortcuts import redirect
from django.shortcuts import get_object_or_404
from django.contrib import messages as django_messages
from utilities import logs_manager, utils
from django.db.models import Case, When, Value, IntegerField
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from utilities.services_app import delete_file_by_url
from app_msgtemplates.models import MsgTemplate


logs_manager = LogsManager(params.logs_dir)
messages_obj = custom_messages.Messages()

@login_required
def get_msgtemplates(request):
    try:
        msgtemplates = MsgTemplate.objects.all()
        msgtemplates = msgtemplates.filter(administrator_id=request.user.administrator_id)

        query_params = request.GET.dict()
        filters, context_filters, custom_q_filters = prepare_filters_and_context(query_params, "MsgTemplate")
        

        order_by_param = request.GET.get('order_by', '')
        if order_by_param:
            ordering = prepare_order(request, order_by_param)
            field, _ = order_by_param.split('__')
            msgtemplates = msgtemplates.filter(**filters).annotate(
                null_priority=Case(
                    When(**{f"{field}__isnull": True}, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField()
                )
            ).order_by('null_priority', ordering)
        else:
            msgtemplates = msgtemplates.filter(**filters)

        context = {
            'page_obj': pagination(request, msgtemplates, 30),
            'order_by': order_by_param,
            **context_filters,
            'filters': FILTERS,
            'columns': COLUMNS,
            'form_fields': FORM_FIELDS,
            'addModalId': ADD_MODAL_ID,
            'editModalId': EDIT_MODAL_ID,
            'add_button_label': 'Νέο Template',
            'add_modal_title': 'Νέο Template',
            'edit_modal_title': 'Επεξεργασία Template',
            'modal_description': '',
            'model_name': 'MsgTemplate',
            'page_title': 'Templates - Πρότυπα Μηνυμάτων',
            'table_id': 'msgtemplates_table',
            'get_action': 'get_msgtemplates',
            'modal_delete_obj_description': '',
            'create_update_url': 'createUpdateMsgTemplate',  # Use the URL name as a string, not the result of reverse()
            'delete_url': 'delete_MsgTemplate'  # Use the URL name as a string, not the result of reverse()
        }

        return render(request, 'msgtemplates.html', context)

    except Exception as e:
        traceback.print_exc()
        return render(request, 'msgtemplates.html', {
            'error_message': f"Σφάλμα: {str(e)}"
        })


def createUpdateMsgTemplate(request):
    try:
        if request.method == 'POST':
            print("REQUEST PATH: " + request.path)
            # Get all form data dynamically
            form_data = {key: value.strip() if isinstance(value, str) else value 
                         for key, value in request.POST.items()}
            
            # Handle file uploads
            for key, file in request.FILES.items():
                form_data[key] = file
                
            status, message = createOrUpdate(request, form_data)
            if status == 200 or status == 201:
                # Add success message
                messages.success(request, "Το πρότυπο μηνύματος αποθηκεύτηκε με επιτυχία!")
                return redirect('get_msgtemplates')
            else:
                # Add error message
                context = {
                    'error_message': message
                }
                # Pass it to the template
                return render(request, 'msgtemplates.html', context)

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        context = {
            'error_message': messages_obj.exception(e, params.default_language)
        }
        return render(request, 'msgtemplates.html', context)


@login_required
def delete_msgtemplate(request):
    model_class = MsgTemplate
    try:
        if request.method == 'POST':
            obj_id = request.POST.get('obj_id')
            print("delete_MsgTemplate with id: " + obj_id)

            if not obj_id:
                django_messages.error(request, "Δεν δόθηκε έγκυρο ID για διαγραφή.")
                return redirect('get_msgtemplates')

            obj = get_object_or_404(model_class, id=obj_id)
            print(obj)

            # ✅ Διαγραφή logo αρχείου αν υπάρχει
            # if obj.logo and hasattr(obj.logo, 'url'):
            #     from utils.file_utils import delete_file_by_url
            #     delete_file_by_url(obj.logo.url)

            obj.delete()
            django_messages.success(request, "Το αντικείμενο διαγράφηκε με επιτυχία!")
        else:
            django_messages.error(request, "Μη υποστηριζόμενη μέθοδος.")

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        django_messages.error(request, messages_obj.exception(e, params.default_language))

    return redirect('get_msgtemplates') 
