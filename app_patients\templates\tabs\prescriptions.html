{% load static %}
{% load custom_filters %}
<div class="row">
    <!-- 🩺 Left: Prescriptions list -->
    <div class="col-md-6">
        <div style="max-height: 60vh; overflow-y: auto; padding-right: 10px;">
            {% for prescription in prescriptions %}
                <div class="card mb-3 border-top-5"
                     style="border-top: 10px solid #6AC7BA;
                            border: 1px solid #6AC7BA">
                    <div class="text-center fw-bold text-white p-2"
                         style="background-color: #6AC7BA">
                        {{ prescription.date|format_date_gr }}
                        <br>
                        <small class="text-white">{{ prescription.patient_name }}</small>
                    </div>
                    <div class="card-body text-center">
                        <img src="data:image/png;base64,{{ prescription.barcode_base64 }}"
                             alt="Barcode"
                             class="mb-1"
                             style="max-height: 100px">
                        <!-- <div class="text-muted small fw-bold">{{ prescription.barcode_number }}</div> -->
                    </div>
                    <div class="card-footer bg-white d-flex justify-content-between">
                        <div>
                            <strong>Συνταγογράφος:</strong>
                            <br>
                            <span style="color: #7CCEC2;" class="fw-bold">{{ prescription.prescriber_name }}</span>
                        </div>
                        <div>
                            <strong>Φαρμακείο:</strong>
                            <br>
                            <span style="color: #7CCEC2;" class="fw-bold">{{ prescription.pharmacy_name }}</span>
                        </div>
                    </div>
                </div>
            {% empty %}
                <p>Δεν υπάρχουν ακόμη συνταγογραφήσεις.</p>
            {% endfor %}
        </div>
    </div>
    <!-- 🖼️ Right: Image and Button -->
    <div class="col-md-6 d-flex flex-column align-items-center justify-content-center ">
        <img src="{% static 'images/prescriptions.png' %}"
             alt="Εικονογράφηση Συνταγών"
             class="img-fluid mb-4"
             style="max-height: 320px;
                    object-fit: contain">
        <button class="btn medical_note_btn px-4 py-3 mt-4"
                data-bs-toggle="modal"
                data-bs-target="#prescriptionModal">
            Νέα Συνταγογράφηση <i class="bi bi-plus-circle ms-1"></i>
        </button>
    </div>
</div>
<!-- Modal template for adding prescription -->
<div class="modal fade"
     id="prescriptionModal"
     tabindex="-1"
     aria-labelledby="prescriptionModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <form method="POST"
              action="{% url 'patient_tab_content' patient.id 'prescriptions' %}"
              class="w-100">
            {% csrf_token %}
            <div class="modal-content p-3 border-1">
                <div class="modal-header ">
                    <h5 class="modal-title">Νέα Συνταγογράφηση</h5>
                    <button type="button"
                            class="btn-close"
                            data-bs-dismiss="modal"
                            aria-label="Κλείσιμο"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Ασθενής</label>
                        <input type="text"
                               class="form-control rounded-pill"
                               value="{{ patient.first_name }} {{ patient.last_name }}"
                               disabled>
                    </div>
                    <div class="mb-3">
                        <label for="{{ prescription_form.barcode_number.id_for_label }}"
                               class="form-label">Αριθμός Barcode</label>
                        {{ prescription_form.barcode_number }}
                    </div>
                    <div class="mb-3">
                        <label for="{{ prescription_form.prescriber_name.id_for_label }}"
                               class="form-label">Συνταγογραφών</label>
                        {{ prescription_form.prescriber_name }}
                    </div>
                    <div class="mb-3">
                        <label for="{{ prescription_form.pharmacy_name.id_for_label }}"
                               class="form-label">Φαρμακείο</label>
                        {{ prescription_form.pharmacy_name }}
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="submit" class="btn btn-dark rounded-pill px-4">Αποθήκευση</button>
                </div>
            </div>
        </form>
    </div>
</div>
