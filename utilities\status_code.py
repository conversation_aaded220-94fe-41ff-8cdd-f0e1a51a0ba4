
class StatusCode:
    # 1xx Informational responses
    # 2xx Successful responses
    ok_200 = 200  # The request was fulfilled.
    created_201 = 201
    accepted_202 = 202
    non_authoritative_information_203 = 203
    no_content_204 = 204
    # 3xx Redirection messages
    # 4xx Client error responses
    bad_request_400 = 400
    unauthorized_401 = 401
    # payment_required_402 = 402
    forbidden_403 = 403
    not_found_404 = 404
    not_acceptable_406 = 406  # This response is sent when the web server, after performing server-driven content negotiation, doesn't find any content that conforms to the criteria given by the user agent.
    conflict_409 = 409  # This response is sent when a request conflicts with the current state of the server (e.g., user already exists).
    # 55xx Internal Server Error
    internal_server_error_500 = 500
    service_unavailable_503 = 503

    class CommonUsedStatusCode:
        ok_200 = 200
        bad_request_400 = 400
        unauthorized_401 = 401
        not_found_404 = 404
        not_acceptable_406 = 406
        conflict_409 = 409