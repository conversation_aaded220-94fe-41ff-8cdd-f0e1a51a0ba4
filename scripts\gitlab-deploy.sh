#!/bin/bash
# Script to deploy changes to GitLab with a user-provided commit message

# Get the directory of this script (so it works even if run from another location)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"  # Move one level up to the project root

# Navigate to the Django project root
cd "$PROJECT_DIR" || exit 1

echo "Current Git status:"
git status

# Ask for user confirmation to proceed
read -p "Do you want to continue with the deployment? (y/n): " confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    # Prompt for a commit message
    echo "You need to enter a commit message to continue with the deployment."
    read -p "Enter your commit message: " commit_message

    # Check if the commit message is empty
    if [[ -z "$commit_message" ]]; then
        echo "Commit message cannot be empty. Deployment aborted."
        exit 1
    fi

    # Proceed with Git operations
    echo "Adding all changes to staging..."
    git add .

    echo "Committing changes..."
    git commit -m "$commit_message"

    echo "Pushing to remote..."
    git push
    echo "✅ Deployment completed successfully."
else
    echo "❌ Deployment canceled."
fi