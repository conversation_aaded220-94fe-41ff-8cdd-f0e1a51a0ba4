from django.shortcuts import render

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from utilities.services_app import prepare_filters_and_context, prepare_order, pagination
from app_calls_center.table_configs.tconf_app_calls_center import FILTERS, COLUMNS, FORM_FIELDS, ADD_MODAL_ID, EDIT_MODAL_ID
import traceback
from utilities import messages as custom_messages  # ✅ your Messages class
from utilities import parameters as params
from utilities.services_app import createOrUpdate
from utilities.logs_manager import LogsManager
from django.contrib import messages
from django.shortcuts import redirect
from django.shortcuts import get_object_or_404
from django.contrib import messages as django_messages
from utilities import logs_manager, utils
from django.db.models import Case, When, Value, IntegerField
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from utilities.services_app import delete_file_by_url
from app_calls_center.models import CallsCenter
from app_users.models import User   
from django.db.models import Q
from datetime import datetime




logs_manager = LogsManager(params.logs_dir)
messages_obj = custom_messages.Messages()

@login_required
def get_calls_center(request):
    try:
        calls_centers = CallsCenter.objects.all()
        calls_centers = calls_centers.filter(administrator_id=request.user.administrator_id)

        query_params = {
            k: v for k, v in request.GET.items()
            if v is not None and v.strip() != '' and v.strip().lower() != 'none'
        }

                # Normalize date and time filters to DB-compatible formats
        if 'date' in query_params:
            try:
                query_params['date'] = datetime.strptime(query_params['date'], '%d/%m/%Y').date().isoformat()
            except ValueError:
                del query_params['date']  # Invalid format, skip filter

        if 'time' in query_params:
            try:
                query_params['time'] = datetime.strptime(query_params['time'], '%H:%M').time().isoformat()
            except ValueError:
                del query_params['time']

        filters, context_filters, custom_q_filters = prepare_filters_and_context(query_params, "CallsCenter")

        # Handle custom user search
        user_search = query_params.get("user_search")
        if user_search:
            calls_centers = calls_centers.filter(
                Q(user__first_name__icontains=user_search) |
                Q(user__last_name__icontains=user_search) |
                Q(user__email__icontains=user_search)
            )

        order_by_param = request.GET.get('order_by', '')
        if order_by_param:
            ordering = prepare_order(request, order_by_param)
            field, _ = order_by_param.split('__')
            calls_centers = calls_centers.filter(custom_q_filters, **filters).annotate(
                null_priority=Case(
                    When(**{f"{field}__isnull": True}, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField()
                )
            ).order_by('null_priority', ordering)
        else:
            calls_centers = calls_centers.filter(custom_q_filters, **filters)

        
        # Get all patients for the current administrator to populate the dropdown
        users = User.objects.filter(administrator_id=request.user.administrator_id)

        # Create a copy of the form fields to modify
        form_fields = FORM_FIELDS.copy()
        
        for field in form_fields:
            if field['id'] == 'user':
                field['options'] = [
                    {'value': user.id, 'label': f"{user.last_name} {user.first_name} ({user.email})"}
                    for user in users
                ]
                break

        context = {
            'page_obj': pagination(request, calls_centers, 30),
            'order_by': order_by_param,
            **context_filters,
            'filters': FILTERS,
            'columns': COLUMNS,
            'form_fields': form_fields,  # Use the modified form fields
            'addModalId': ADD_MODAL_ID,
            'editModalId': EDIT_MODAL_ID,
            'add_button_label': 'Νέα Καταχώρηση',
            'add_modal_title': 'Προσθήκη Καταχώρησης',
            'edit_modal_title': 'Επεξεργασία Καταχώρησης',
            'modal_description': 'Συμπληρώστε τα παρακάτω στοιχεία για να προσθέσετε μια νέα καταχώρηση.',
            'model_name': 'CallsCenter',
            'page_title': 'Τηλεφωνικό Κέντρο',
            'table_id': 'calls_centers_table',
            # 'get_action': 'get_calls_center',
            'modal_delete_obj_description': '',
            'create_update_url': 'createUpdateCallsCenter',  # Use the URL name as a string, not the result of reverse()
            'delete_url': 'delete_calls_center',  # Use the URL name as a string, not the result of reverse()
        }

        return render(request, 'calls_center.html', context)

    except Exception as e:
        traceback.print_exc()
        return render(request, 'calls_center.html', {
            'error_message': f"Σφάλμα: {str(e)}"
        })


def createUpdateCallsCenter(request):
    try:
        if request.method == 'POST':
            print("REQUEST PATH: " + request.path)
            # Get all form data dynamically
            form_data = {key: value.strip() if isinstance(value, str) else value 
                         for key, value in request.POST.items()}
            
            # Handle file uploads
            for key, file in request.FILES.items():
                form_data[key] = file
            
            user_id = form_data.pop('user', None)
            
            # Create or update the calls_center
            if 'id' in form_data and form_data['id']:
                # Update existing calls_center
                calls_center_id = form_data.pop('id')
                calls_center = CallsCenter.objects.get(id=calls_center_id)
                
                # Update fields
                for key, value in form_data.items():
                    if key not in ['csrfmiddlewaretoken'] and hasattr(calls_center, key):
                        if key == 'date' and value:
                            value = datetime.strptime(value, '%d/%m/%Y').date()
                        elif key == 'time' and value:
                            value = datetime.strptime(value, '%H:%M').time()
                        setattr(calls_center, key, value)

                
                # Update user if provided
                user_id = form_data.pop('user', None)

                if user_id:
                    from app_users.models import User
                    try:
                        user_instance = User.objects.get(id=user_id)
                        calls_center.user = user_instance
                    except User.DoesNotExist:
                        messages.error(request, "Ο χρήστης δεν βρέθηκε!")
                        return redirect('get_calls_center')

                
                # Ensure administrator_id is set
                calls_center.administrator_id = request.user.administrator_id
                
                calls_center.save()
                messages.success(request, "Η συνταγογράφηση ενημερώθηκε με επιτυχία!")
            else:
                # Create new calls_center
                try:
                    # Get the user instance
                    if not user_id:
                        messages.error(request, "Παρακαλώ επιλέξτε χρήστη!")
                        return redirect('get_calls_center')

                    try:
                        from app_users.models import User
                        user_instance = User.objects.get(id=user_id)

                        # Δημιουργία νέας εγγραφής στο τηλεφωνικό κέντρο
                        date_value = form_data.get('date')
                        time_value = form_data.get('time')
                        general_info = form_data.get('general_info', '')

                        calls_center = CallsCenter(
                            user=user_instance,
                            administrator_id=request.user.administrator_id,
                            date=datetime.strptime(date_value, '%d/%m/%Y').date() if date_value else None,
                            time=datetime.strptime(time_value, '%H:%M').time() if time_value else None,
                            general_info=general_info
                        )

                        calls_center.save()
                        messages.success(request, "Η καταγραφή δημιουργήθηκε με επιτυχία!")

                    except User.DoesNotExist:
                        messages.error(request, "Ο χρήστης δεν βρέθηκε!")
                        return redirect('get_calls_center')

                  
                except Exception as e:
                    traceback.print_exc()
                    logs_manager.write_error_logs(traceback.format_exc())
                    messages.error(request, f"Σφάλμα: {str(e)}")
                    return redirect('get_calls_center')
            
            return redirect('get_calls_center')
        else:
            messages.error(request, "Μη έγκυρη μέθοδος!")
            return redirect('get_calls_center')

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        messages.error(request, f"Σφάλμα: {str(e)}")
        return redirect('get_calls_center') 


@login_required
def delete_calls_center(request):
    model_class = CallsCenter
    try:
        if request.method == 'POST':
            obj_id = request.POST.get('obj_id')
            print("delete_calls_center with id: " + obj_id)

            if not obj_id:
                django_messages.error(request, "Δεν δόθηκε έγκυρο ID για διαγραφή.")
                return redirect('get_calls_center')

            obj = get_object_or_404(model_class, id=obj_id)
            print(obj)

            # ✅ Διαγραφή logo αρχείου αν υπάρχει
            # if obj.logo and hasattr(obj.logo, 'url'):
            #     from utils.file_utils import delete_file_by_url
            #     delete_file_by_url(obj.logo.url)

            obj.delete()
            django_messages.success(request, "Το αντικείμενο διαγράφηκε με επιτυχία!")
        else:
            django_messages.error(request, "Μη υποστηριζόμενη μέθοδος.")

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        django_messages.error(request, messages_obj.exception(e, params.default_language))

    return redirect('get_calls_center') 
