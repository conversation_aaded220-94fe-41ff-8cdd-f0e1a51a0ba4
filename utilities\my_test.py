import utils
from airos import settings
from django.utils import timezone
from datetime import datetime, timedelta, timezone, tzinfo

if False:
    print(datetime.now(tz=timezone.utc if settings.USE_TZ else None))



if False:
    from datetime import datetime, timedelta, timezone, tzinfo
    token_expires_in_minutes = 75 #mins
    add_time = timedelta( days=1, seconds=0, microseconds=0,
                milliseconds=0, minutes=0, hours=0, weeks=0)
    current_time = datetime.now()

    new_time = time + time_change


    print(new_time > current_time)
    print(new_time < current_time)
    print(new_time == current_time)


import xlrd
class ReadExcel:
    def read_excel_worksheet(self, xls_file_path, sheet_name=None, sheet_index=None):
        worksheet = None
        workbook = xlrd.open_workbook(xls_file_path)
        if sheet_name:
            worksheet = workbook.sheet_by_name(sheet_name)
        elif sheet_index:
            worksheet = workbook.sheet_by_index(sheet_index)
        else:
            print(f'Please set sheet_name or sheet_index because sheet_name={sheet_name} and sheet_index={sheet_index}.')
        return workbook, worksheet

    def read_excel_cell(self, worksheet, row, column):
        return worksheet.cell(row, column).value

    def read_headers_index_of_worksheet(self, header_row=1, num_of_columns=25):
        koinontita_dimos_set = set()
        workbook, worksheet = self.read_excel_worksheet(
            xls_file_path='D:/GoogleDrivePNG/Codnext-shared/Dromologitis/kallikraths_kwdikologio1_31_5_11.xls',
            sheet_index=1)
        index_header_list = []
        row=header_row
        index = 0
        for col in range(0,num_of_columns):
            value = self.read_excel_cell(worksheet, row, col)
            index_header_list.append((index, value))
            index += 1
        for (i, h) in index_header_list:
            print(f'(\'{i}\', \'{h}\'),')

    def read_periferia_dimos_form_excel(self):
        koinontita_dimos_set = set()
        workbook, worksheet =self.read_excel_worksheet(xls_file_path='D:/GoogleDrivePNG/Codnext-shared/Dromologitis/kallikraths_kwdikologio1_31_5_11.xls', sheet_index=1)
        for row in range(2, 6163):
            t = ()
            for col in [12, 6]:
                value = self.read_excel_cell(worksheet, row, col)
                t = t + (value,)
            # print(t)
            koinontita_dimos_set.add(t)
        koinontita_dimos_list = []
        for (k, d) in koinontita_dimos_set:
            koinontita_dimos_list.append((k, d))
        koinontita_dimos_list = utils.sort_list_of_string_tuples_by_fist_and_second_element(koinontita_dimos_list)
        for (k, d) in koinontita_dimos_list:
            print(f'(\'{k}\', \'{d}\'),')

    def read_header_data_form_excel(self, column_index=12):
        column_value_set = set()
        column_value_list = []
        workbook, worksheet =self.read_excel_worksheet(xls_file_path='D:/GoogleDrivePNG/Codnext-shared/Dromologitis/kallikraths_kwdikologio1_31_5_11.xls', sheet_index=1)
        for row in range(2, 6163):
            value = self.read_excel_cell(worksheet, row, column_index)
            column_value_set.add(value)
        for v in column_value_set:
            column_value_list.append(v)
        column_value_list = utils.sort_list_of_string(column_value_list, reverse=False)
        for v in column_value_list:
            print(f'\'{v}\',')
        print(len(column_value_list))







a = ReadExcel()
#a.read_headers_index_of_worksheet()
a.read_header_data_form_excel(column_index=12)
# a.read_periferia_dimos_form_excel()


