function openModal(modalname) {
    // Open the modal
    let modal = new bootstrap.Modal(document.getElementById(modalname));
    modal.show();
}

//function openModal(modalname, modalTitle) {
//    // Open the modal
//    let modal = new bootstrap.Modal(document.getElementById(modalname));
//    let modalTitle = document.getElementById(modalname + "_title");
//    modalTitle.innerHTML = modalTitle;
//    modal.show();
//}


//     function openDeleteModal(tablename, id, value1 = '', modal_id = 'deleteModal') {
//             const modalEl = document.getElementById(modal_id);

//             if (!modalEl) {
//             console.error(`Modal element not found: ${modal_id}`);
//             return;
//         }

//         modalEl.querySelector('#tablename_delete_modal').value = tablename;
//         modalEl.querySelector('#obj_id').value = id;

//         const infoEl = modalEl.querySelector('#value1');
//         if (infoEl) {
//             infoEl.textContent = value1;
//             infoEl.style.display = value1 ? 'block' : 'none';
//         }

//         const modal = new bootstrap.Modal(modalEl);
//         modal.show();
// }


$(document).ready(function() {

/**** File Upload (START) *******************/
   let fileUploadInput = document.getElementById("fileUpload");
   let tablenameInput = document.getElementById("tablename")
   if (fileUploadInput) {
        fileUploadInput.addEventListener("change", function(event) {
            let fileInput = event.target;
            let file = fileInput.files[0];
            let formData = new FormData();

            if (!file) {
                console.error("No file selected.");
                return;
            }

            formData.append("file", file); // Add the file here!

            console.log(document.getElementById("fileUpload").dataset.objectId);

            let object_id = document.getElementById("fileUpload").dataset.objectId;
            let csrfToken = document.getElementById("csrfToken").value;
            let tablename = document.getElementById("tablename").value;
            console.log(document.getElementById("tablename").value);
            formData.append("tablename", tablename);
            if (object_id) {
                formData.append("object_id", object_id);
            }
//            window.location.href = `/viografiko/${object_id}/files`;
//            return;
            fetch(`/upload_file/`, {
                method: "POST",
                body: formData,
                headers: {
                    "X-CSRFToken": csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                window.location.href = `/viografiko/${object_id}/files`;
            })
            .catch(error => console.error("Error:", error));
        });
    } else {
        console.warn("imageUpload element not found.");
    }

});

$(document).ready(function() {
        // Για κάθε option, θα γεμίσουμε τα αστεράκια δυναμικά
        $('#rating option').each(function() {
            var starsCount = $(this).val(); // Παίρνουμε το value της επιλογής (πόσα αστέρια)
            var stars = '';

            // Ελέγχουμε αν το value είναι κενό
            if (starsCount === '') {
                // Αν είναι κενό, παραλείπουμε το option και δεν προσθέτουμε αστεράκι
                return true; // Απλώς παραλείπουμε αυτό το option
            }

            // Αν είναι 0, θα βάλουμε άδειο αστεράκι
            if (starsCount == 0) {
                stars = '☆'; // Άδειο αστεράκι
            }
            else if (starsCount >= 1){
                // Δημιουργούμε τα αστεράκια
                for (var i = 0; i < starsCount; i++) {
                    stars += '★'; // Προσθέτουμε ένα αστεράκι
                }
            }

            // Βάζουμε τα αστεράκια στο option
            $(this).html(stars);
        });

        $('#rating_edit option').each(function() {
            var starsCount = $(this).val();
            var stars = '';

            // Ελέγχουμε αν είναι null/empty
            if (starsCount === '') {
                // Βάζουμε απλό κενό
                $(this).html('');
                return true;
            }

            // Ελέγχουμε αν είναι 0 (ως string!)
            if (starsCount === '0') {
                stars = '☆'; // Άδειο αστεράκι
            } else {
                // Δημιουργούμε γεμάτα αστεράκια
                for (var i = 0; i < parseInt(starsCount); i++) {
                    stars += '★';
                }
            }

            $(this).html(stars);
        });
});

function openEditModal(tablename, fieldname, id, modalname) {
    const url = `/find_obj_with_id/${id}/${tablename}/${fieldname}/`;
    console.log(`Fetching data from: ${url}`);

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log(data);
                // Loop through keys in data.data
                for (let key in data.data) {
                    // Define the edit field id pattern
                    let elementId = `${key}_edit`;
                    let element = document.getElementById(elementId);

                    if (element) {
                        // Skip file inputs to avoid the error
                        if (element.type === 'file') {
                            continue;
                        }
                        
                        // Handle input types differently if needed
                        if (element.type === "checkbox") {
                            element.checked = Boolean(data.data[key]);
                        } else {
                            if (data.data[key] !== null && data.data[key] !== undefined) {
                                element.value = data.data[key];
                            } else {
                                element.value = "";
                            }
                        }
                        console.log("key: " + key + " , value: " + data.data[key]);
                    } else {
                        // Handle dynamic fields, like id_edit_status
                        let dynamicElement = document.getElementById(`${key}_edit_`+ 'status');
                        if (dynamicElement) {
                            dynamicElement.value = data.data[key] || "";
                        }
                        // Handle dynamic fields, like id_edit_status
                        dynamicElement = document.getElementById(`${key}_edit_${tablename}`);
                        if (dynamicElement) {
                            // Skip file inputs to avoid the error
                            if (dynamicElement.type === 'file') {
                                // Don't try to set value on file inputs
                                continue;
                            }
                            
                            // Keep the rest of your existing code
                            if (dynamicElement.type === 'checkbox') {
                                dynamicElement.checked = data.data[key];
                            } else if (dynamicElement.tagName === 'SELECT') {
                                dynamicElement.value = data.data[key] || '';
                            } else if (dynamicElement.tagName === 'TEXTAREA' || dynamicElement.tagName === 'INPUT') {
                                dynamicElement.value = data.data[key] || '';
                            }
                            console.log("dynamic: " + dynamicElement.value);
                        }
                    }

                }
                // Handle special cases if necessary
                let idEdit = document.getElementById("id_edit");
                if (idEdit) idEdit.value = data.data.id || "";

                // Handle special cases if necessary
                let idEdit_tablename = document.getElementById(`id_edit_${tablename}`); // Χρησιμοποιήστε το tablename για το id
                if (idEdit_tablename) idEdit_tablename.value = data.data.id || "";

                // Open modal
                let modalElement = document.getElementById(modalname);
                if (modalElement) {
                    let modal = new bootstrap.Modal(modalElement);
                    modal.show();
                } else {
                    console.error("Modal not found:", modalname);
                }
            } else {
                console.error(data.message);
            }
        }).catch(error => console.error('Error:', error));
}

/*******************   tinymce   *****************/
// Add a check before using tinymce
if (typeof tinymce !== 'undefined') {
    tinymce.init({
        selector: '#email_message',
        menubar: false,
        plugins: 'advlist autolink lists link charmap print preview',
        toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link',
        height: 250
    });
}

function clearFiltersAndSubmit(button) {
    const form = button.closest('form');
    if (!form) return;

    // Clear all inputs/selects inside the form
    form.querySelectorAll('input, select').forEach(el => {
        if (el.type === 'text' || el.type === 'number' || el.tagName === 'SELECT') {
            el.value = '';
        }
    });

    form.submit();
}

