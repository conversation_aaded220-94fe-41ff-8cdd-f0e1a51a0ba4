from django.urls import path
from app_users.views import user_views, auth_views
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # User Management
    path('', user_views.get_users, name='get_users'),
    path('createUpdateUser/', user_views.createUpdateUser, name='createUpdateUser'),
    path('delete_user/', user_views.delete_user, name='delete_user'),

    # Authentication
    # path('login/', auth_views.login_user, name='login'),
    # path('logout/', auth_views.logout_user, name='logout'),
    # path('register/', auth_views.register, name='register'),
    path('fix_user_passwords/', auth_views.fix_user_passwords, name='fix_user_passwords'),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
