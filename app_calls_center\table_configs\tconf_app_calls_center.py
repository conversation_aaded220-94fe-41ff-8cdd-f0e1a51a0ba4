ADD_MODAL_ID = 'addModalId'
EDIT_MODAL_ID = 'editModalId'

COLUMNS = [
    {
        'id': 'id',
        'label': '#',
        'field': 'id',
        'type': 'counter',
        'required': True,
        'default_visible': True
    },
    {
        'id': 'date',
        'label': 'ΗΜΕΡΟΜΗΝΙΑ',
        'field': 'date',
        'type': 'date',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'time',
        'label': 'ΩΡΑ',
        'field': 'time',
        'type': 'time',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'user',
        'label': 'ΧΡΗΣΤΗΣ',
        'field': 'user',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'general_info',
        'label': 'ΓΕΝΙΚΕΣ ΠΛΗΡΟΦΟΡΙΕΣ',
        'field': 'general_info',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'actions',
        'label': 'ΕΝΕΡΓΕΙΕΣ',
        'type': 'actions',
        'required': True,
        'default_visible': True
    }
]

FILTERS = [
    {
        'id': 'order_by',
        'name': 'order_by',
        'label': 'Ταξινόμηση',
        'field': 'order_by',
        'type': 'select',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
        },
        'options': [
            {'value': 'date__asc', 'label': 'Ημερομηνία Αύξουσα ↑'},
            {'value': 'date__desc', 'label': 'Ημερομηνία Φθίνουσα ↓'}
        ]
    },
    {
        'id': 'user_filter',
        'name': 'user__contains',
        'label': 'Χρήστης',
        'field': 'user',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary'
        }
    },
    {
        'id': 'date_filter',
        'name': 'date',
        'label': 'Ημερομηνία',
        'field': 'date',
        'type': 'date',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary flatpickr'
        }
    },
    {
        'id': 'time_filter',
        'name': 'time',
        'label': 'Ώρα',
        'field': 'time',
        'type': 'time',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary flatpickr-time'
        }
    }
]


FORM_FIELDS = [
    {
        'id': 'date',
        'label': 'Ημερομηνία',
        'type': 'date',
        'required': True,
        'width': '6',
        'placeholder': 'Εισάγετε ημερομηνία'
    },
    {
        'id': 'time',
        'label': 'Ώρα',
        'type': 'time',
        'required': True,
        'width': '6',
        'placeholder': 'Εισάγετε ώρα'
    },
    {
        'id': 'user',
        'label': 'Χρήστης',
        'type': 'select',
        'required': True,
        'options': [],  # populate dynamically
        'width': '6',
        'placeholder': 'Επιλέξτε χρήστη'
    },
    {
        'id': 'general_info',
        'label': 'Γενικές Πληροφορίες',
        'type': 'textarea',
        'required': False,
        'width': '12',
        'placeholder': 'Εισάγετε γενικές πληροφορίες'
    }
]










