# Generated by Django 5.2.1 on 2025-06-02 09:26

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SmsTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('administrator_id', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('type', models.CharField(max_length=256)),
                ('name', models.Char<PERSON>ield(max_length=256)),
                ('message', models.TextField()),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
    ]
