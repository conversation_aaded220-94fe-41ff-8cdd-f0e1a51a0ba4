{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}
{% block title %}{{ page_title }}{% endblock %}
{% block content %}
    <div class="container-fluid">
        <h2 class="mb-3">{{ page_title }}</h2>
        <div class="d-flex justify-content-between align-items-center mb-3">
            <form method="get"
                  action="{% url 'get_msgtemplates' %}"
                  class="d-flex align-items-end flex-grow-1 gap-3">
                {% include 'include_files/filters_app_msgtemplates.html' with filters=filters %}
                <div class="d-flex align-items-start gap-2">
                    <button type="submit" class="btn btn-dark rounded-4 mb-3 px-4">
                        <i class="bi bi-search fs-6"></i>
                    </button>
                    <button type="button"
                            class="btn btn-outline-secondary rounded-4 mb-3 px-3"
                            onclick="clearFiltersAndSubmit(this)">
                        <i class="bi bi-x fs-6"></i>
                    </button>
                </div>
            </form>
            <div class="d-flex align-items-center mt-2">
                <!-- Column Selector Button -->
                <div class="dropdown">
                    <!-- <button class="btn btn-outline-secondary" type="button" id="columnSelectorBtn" onclick="toggleColumnSelector()">
                        <i class="fas fa-columns"></i> Στήλες
                    </button> -->
                    <div id="columnSelectorMenu"
                         class="position-absolute bg-white shadow p-3 rounded-4 border"
                         style="display: none;
                                min-width: 250px;
                                z-index: 1000;
                                right: 0;
                                margin-top: 10px">
                        {% for column in columns %}
                            {% if not column.required %}
                                <div class="form-check">
                                    {% if column.default_visible %}
                                        <input class="form-check-input column-toggle"
                                               type="checkbox"
                                               value="{{ column.id }}"
                                               id="column_{{ column.id }}"
                                               checked
                                               data-table-id="{{ table_id }}">
                                    {% else %}
                                        <input class="form-check-input column-toggle"
                                               type="checkbox"
                                               value="{{ column.id }}"
                                               id="column_{{ column.id }}"
                                               data-table-id="{{ table_id }}">
                                    {% endif %}
                                    <label class="form-check-label" for="column_{{ column.id }}">{{ column.label }}</label>
                                </div>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
                <button class="add-btn m-4"
                        onclick="openAddModal('addModalId', '{{ add_modal_title }}', '{{ add_modal_description }}')">
                    {{ add_button_label }}
                    <i class="bi bi-plus fs-2"></i>
                </button>
            </div>
        </div>
        <!-- Dynamic Table -->
        {% include 'include_files/data_table_app_msgtemplates.html' with columns=columns table_id=table_id model_name=model_name %}
        <!-- Modals -->
        {% include 'modals/delete_object_modal_app_msgtemplates.html' with modal_id='deleteModal' model_name=model_name delete_url=delete_url %}
        {% include 'modals/add_edit_modal_app_msgtemplates.html' with modal_id='addModalId' modal_title=add_modal_title form_id='add_form_id' form_fields=form_fields model_name=model_name mode='add' create_update_url=create_update_url %}
        {% include 'modals/add_edit_modal_app_msgtemplates.html' with modal_id='editModalId' modal_title=edit_modal_title form_id='edit_form_id' form_fields=form_fields model_name=model_name mode='edit' create_update_url=create_update_url %}
    </div>
{% endblock %}
{% block js %}
    <script src="{% static 'js/app_msgtemplatesCtrl.js' %}"></script>
    {%comment %}
                                        <script src="{% static 'js/toast_messages.js' %}"></script>
    {% endcomment %}
    <script src="{% static 'js/column_selector_app_msgtemplates.js' %}"></script>
{% endblock %}
