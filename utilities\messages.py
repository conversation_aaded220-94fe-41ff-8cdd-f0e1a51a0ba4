class Messages:
    e = 'en'
    g = 'el'
    entry_update_success = {e: 'The entry updated successfully!', g: 'Η εγγραφή ενημερώθηκε επιτυχώς'}

    entry_not_found = {e: 'The entry not found!', g: 'Η εγγραφή δεν βρέθηκε'}

    store_entry_found = {e: 'The store entry found!', g: 'Το κατάστημα  βρέθηκε!'}

    store_entry_not_found = {e: 'The store entry not found!', g: 'Το κατάστημα δεν βρέθηκε'}
    store_deletion = {e: 'The store is deleted!', g: 'Το κατάστημα διαγράφηκε!'}
    product_not_received = {e: 'No product received!', g: 'Κανένα προϊόν δεν λήφθηκε'}

    no_valid_xml = {e: 'No valid xml!', g: 'Μη έγκυρο XML'}

    user_not_found = {e: 'The entry not found!', g: 'Ο χρήστης δεν βρέθηκε'}
    something_went_wrong = {e: 'Something went wrong!', g: 'Κάτι ΔΕΝ πήγε καλά!'}
    invalid_password = {e: 'Invalid password!',
                        g: 'Μη έγκυρος κωδικός πρόσβασης!'}
    change_password_success = {e: 'Password changed successfully!',
                               g: 'Ο κωδικός πρόσβασης έχει αλλάξει με επιτυχία!'}
    valid_credentials = {e: 'Valid credentials', g: 'Έγκυρα στοιχεία πρόσβασης!', }
    valid_email = {e: 'Valid email', g: 'Έγκυρο email!', }
    invalid_email = {e: 'Invalid email', g: 'Μη έγκυρο email', }
    invalid_credentials = {e: 'Invalid credentials', g: 'Μη έγκυρα στοιχεία πρόσβασης', }
    valid_token = {e: 'Valid token', g: 'Έγκυρο λεκτικό (token)'}
    invalid_token = {e: 'Invalid token', g: 'Μή έγκυρο λεκτικό (token)'}
    null_token = {e: 'Null token. Please login.',
                  g: 'Κενό λεκτικό (token). Παρακαλώ προχωρήστε σε σύνδεση χρήστη (login).'}
    valid_token_without_login = {e: 'Valid token without login', g: 'Έγκυρο λεκτικό (token) χωρίς σύνδεση'}
    invalid_token_without_login = {e: 'Invalid token without login', g: 'Μή έγκυρο λεκτικό (token) χωρίς σύνδεση'}
    null_token_without_login = {e: 'Null token without login', g: 'Κενό λεκτικό (token) χωρίς σύνδεση'}
    logout_success = {e: 'Logout has done!', g: 'Η αποσύνδεση έγινε!'}
    logout_had_done = {e: 'Instance not found, logout was already done!',
                       g: 'Δεν βρέθηκε σύνδεση, η αποσύνδεση έχε ήδη γίνει!'}
    logout_from_all_devices_success = {e: 'Logout from all devices has done successfully!',
                                       g: 'Η αποσύνδεση από όλες τις συσκευές ολοκληρώθηκε με επιτυχία!'}
    logout_all_users_success = {e: 'Logout all users has done successfully!',
                                g: 'Η αποσύνδεση όλων των χρηστών ολοκληρώθηκε με επιτυχία!'}
    registration_success = {e: 'Successful registration! Go back to Login', g: 'Επιτυχής εγγραφή! Πήγαινε πίσω στη Σύνδεση για να συνδεθείς'}
    entry_obtained_successfully = {e: 'Entry obtained successfully!', g: 'Η εγγραφή ανακτήθηκε επιτυχώς!'}
    entry_added_successfully = {e: 'Entry added successfully!', g: 'Η εγγραφή καταχωρήθηκε με επιτυχία!'}
    entry_not_added = {e: 'No Entry added!', g: 'Η εγγραφή ΔΕΝ καταχωρήθηκε!'}
    entry_updated_successfully = {e: 'Entry updated successfully!', g: 'Η εγγραφή ενημερώθηκε με επιτυχία!'}
    entry_removed_successfully = {e: 'Entry removed successfully!', g: 'Η εγγραφή διαγράφηκε με επιτυχία!'}
    entries_removed_successfully = {e: 'Entries deleted successfully!', g: 'Οι εγγραφές διαγράφηκαν επιτυχώς!'}
    update_user_details_success = {e: 'User details updated Successfully!',
                                   g: 'Επιτυχής ενημέρωση στοιχείων χρήστη!'}
    code_has_sent_to_email = {e: 'The code has sent to your email!', g: 'Ο κωδικός έχει σταλεί στο email σας!'}
    verification_code_has_expired = {e: 'Verification code has expired!', g: 'Ο κωδικός επαλήθευσης έχει λήξει!'}
    verification_code_valid = {e: 'Valid verification code', g: 'Έγκυρος κωδικός επαλήθευσης!'}
    verification_code_invalid = {e: 'invalid verification code', g: 'Μη Έγκυρος κωδικός επαλήθευσης!'}
    email_not_recognized = {e: 'If you do not recognize the origin of this email, you can safely ignore it.',
                            g: 'Αν δεν αναγνωρίζετε την προέλευση αυτού του email, μπορείτε να το αγνοήσετε με ασφάλεια.'}
    email_no_reply = {e: 'Please do not reply to this email.', g: 'Παρακαλώ μην απαντήσετε σε αυτό το e-mail.'}
    invalid_user = {e: 'invalid user', g: 'Ο χρήστης δεν έχει δικαίωμα πρόσβασης!'}
    unauthorized_user = {e: 'unauthorized user.', g: 'Ο χρήστης δεν έχει δικαίωμα εκτέλεσης αυτής της ενέργειας!'}
    pin_is_enabled = {e: 'PIN is enabled.', g: 'Το PIN είναι ενεργοποιημένο.'}
    pin_is_disabled = {e: 'PIN is disabled.', g: 'Το PIN είναι απενεργοποιημένο.'}
    pin_is_valid = {e: 'PIN is valid.', g: 'Έγκυρο PIN.'}
    pin_is_invalid = {e: 'PIN is invalid.', g: 'Μη έγκυρο PIN.'}
    pin_is_unaccepted = {e: 'Unaccepted PIN, please enter another PIN.',
                         g: 'Μη αποδεκτό PIN, παρακαλώ δώστε ένα άλλο PIN.'}
    pin2_is_enabled = {e: 'PIN 2 is enabled.', g: 'Το PIN 2 είναι ενεργοποιημένο.'}
    pin2_is_disabled = {e: 'PIN 2 is disabled.', g: 'Το PIN 2 είναι απενεργοποιημένο.'}
    pin2_is_valid = {e: 'PIN 2 is valid.', g: 'Έγκυρο PIN 2.'}
    pin2_is_invalid = {e: 'PIN 2 is invalid.', g: 'Μη έγκυρο PIN 2.'}
    pin2_is_unaccepted = {e: 'Unaccepted PIN 2, please enter another PIN 2.',
                          g: 'Μη αποδεκτό PIN 2, παρακαλώ δώστε ένα άλλο PIN 2.'}
    message_reported_for_offensive_or_unacceptable_content = {
        e: 'The message reported for offensive or unacceptable content.',
        g: 'Το μήνυμα αναφέρθηκε για προσβλητικό ή απαράδεκτο περιεχόμενο.'}
    report_was_removed = {
        e: 'The report was removed.',
        g: 'Η αναφορά αφαιρέθηκε.'}
    message_not_found = {e: 'The message not found!', g: 'Το μήνυμα δεν βρέθηκε!'}
    user_block_successfully = {e: 'User blocking completed successfully!',
                               g: 'Ο αποκλεισμός χρήστη ολοκληρώθηκε με επιτυχία!'}
    users_block_successfully = {e: 'Users blocking completed successfully!',
                                g: 'Ο αποκλεισμός χρηστών ολοκληρώθηκε με επιτυχία!'}
    user_unblocked_successfully = {e: 'User unblocked successfully!',
                                   g: 'Ο αποκλεισμός του χρήστη αναιρέθηκε με επιτυχία!'}
    chat_cannot_created_as_user_has_block_other_user = {
        e: 'The chat cannot be created as among the users there is at least one user who has blocked another user!',
        g: 'Η συνομιλία δεν μπορεί να δημιουργηθεί καθώς μεταξύ των χρηστών υπάρχει τουλάχιστον ένας χρήστης που έχει μπλοκάρει άλλον χρήστη!'}
    message_has_sent_successfully = {e: 'The message was sent successfully!',
                                     g: 'To μήνυμα εστάλη με επιτυχία!'}
    message_has_updated_successfully = {e: 'The message has updated successfully!',
                                        g: 'To μήνυμα ενημερώθηκε με επιτυχία!'}
    message_forwarded_successfully = {e: 'Message forwarded successfully!',
                                      g: 'Το μήνυμα προωθήθηκε με επιτυχία!'}
    chat_not_found = {e: 'Chat not found.', g: 'Η συνομιλία δεν βρέθηκε.'}
    chats_not_found = {e: 'No chats found.', g: 'Δεν βρέθηκαν συνομιλίες.'}
    chat_already_exists = {e: 'Chat already exists!', g: 'Η συνομιλία υπάρχει ήδη!'}
    chat_created_successfully = {e: 'Chat created successfully!',
                                 g: 'Η συνομιλία δημιουργήθηκε με επιτυχία!'}
    chat_retrieved_successfully = {e: 'Chat retrieved successfully!',
                                   g: 'Η συνομιλία ανακτήθηκε με επιτυχία!'}
    no_messages_to_delete = {e: "No messages to delete!",
                             g: "Δεν υπάρχουν μηνύματα προς διαγραφή!"}
    delete_messages_successfully_completed = {e: 'Delete messages successfully completed!',
                                              g: 'Η διαγραφή μηνυμάτων ολοκληρώθηκε επιτυχώς!'}
    leaving_chat_successfully_completed = {e: 'Leaving from chat successfully completed!',
                                           g: 'Η αποχώρηση ολοκληρώθηκε επιτυχώς!'}
    chat_cannot_be_deleted_as_it_was_not_created_by_you = {e: 'Chat cannot be deleted as it was not created by you!',
                                                           g: 'Η συνομιλία δεν μπορεί να διαγραφεί καθώς δεν έχει δημιουργηθεί από εσάς!'}
    you_do_not_have_permission_to_delete_chats = {e: 'You do not have permission to delete chats!',
                                                  g: 'Δεν έχετε δικαιώματα διαγραφής συνομιλιών!'}
    the_message_cannot_be_deleted_as_it_was_not_created_by_you = {
        e: 'The message cannot be deleted as it was not created by you!',
        g: 'To μήνυμα δεν μπορεί να διαγραφεί καθώς δεν έχει δημιουργηθεί από εσάς!'}
    user_to_whom_the_message_is_forwarded_was_not_found = {
        e: 'The user to whom the message is forwarded was not found!',
        g: 'Ο χρήστης, στον οποίο προωθείται το μήνυμα, δεν βρέθηκε!'}
    enter_a_valid_time_interval = {
        e: "Please enter a valid time interval (not zero).",
        g: "Παρακαλώ καταχωρήστε ένα έγκυρο χρονικό διάστημα (όχι μηδενικό)."}
    there_are_users_logged_in = {
        e: 'There are users logged in.',
        g: 'Υπάρχουν συνδεδεμένοι χρήστες.'}

    all_users_are_logged_out = {
        e: 'All users are logged out!',
        g: 'Όλοι οι χρήστες είναι αποσυνδεδεμένοι!'}
    user_is_logged_in = {
        e: 'User is logged in',
        g: 'Ο χρήστης είναι συνδεδεμένος'}
    user_is_logged_out = {
        e: 'User is logged out',
        g: 'Ο χρήστης είναι αποσυνδεδεμένος'}

    def exception(self, error, en_or_gr='en'):
        d = {self.e: f'Something went wrong, exception: {error}',
             self.g: f'Κάτι δεν πήγε καλά, σφάλμα: {error}'}
        return d[en_or_gr]

    def user_with_email_already_exists(self, email, en_or_gr='en'):
        d = {self.e: f'User (with email: {email}) already exists!',
             self.g: f'Ο χρήστης (με το email: {email}) έχει ήδη εγγραφεί!'}
        return d[en_or_gr]

    def user_with_username_already_exists(self, username, en_or_gr='en'):
        d = {self.e: f'User (with username: {username}) already exists!',
             self.g: f'Μη διαθέσιμο username ({username})!'}
        return d[en_or_gr]

    def user_with_tel_already_exists(self, tel, en_or_gr='en'):
        d = {self.e: f'User (with email: {tel}) already exists!',
             self.g: f'Ο χρήστης (με το τηλ.: {tel}) έχει ήδη εγγραφεί!'}
        return d[en_or_gr]

    def user_with_afm_already_exists(self, afm, en_or_gr='en'):
        d = {self.e: f'User (with VAT code: {afm}) already exists!',
             self.g: f'Ο χρήστης (με το ΑΦΜ: {afm}) έχει ήδη εγγραφεί!'}
        return d[en_or_gr]

    def user_with_pei_already_exists(self, pei, en_or_gr='en'):
        d = {self.e: f'User (with driver ID (ΠΕΙ): {pei}) already exists!',
             self.g: f'Ο χρήστης (με το ΠΕΙ: {pei}) έχει ήδη εγγραφεί!'}
        return d[en_or_gr]

    def user_with_other_email_and_this_tel_already_exists(self, tel, en_or_gr='en'):
        d = {self.e: f'This phone number ({tel}) is already registered in another entry.',
             self.g: f'Αυτός ο αριθμός τηλεφώνου ({tel}) είναι ήδη καταχωρημένος σε άλλη εγγραφή.'}
        return d[en_or_gr]

    def user_with_this_username_already_exists(self, username, en_or_gr='en'):
        d = {self.e: f'This username ({username}) is already registered in another entry.',
             self.g: f'Αυτό το όνομα χρήστη ({username}) δεν είναι διαθέσιμο.'}
        return d[en_or_gr]

    def user_with_this_email_already_exists(self, email, en_or_gr='en'):
        d = {self.e: f'This e-mail ({email}) is already registered in another entry.',
             self.g: f'Αυτό το e-mail ({email}) δεν είναι διαθέσιμο.'}
        return d[en_or_gr]

    def user_with_other_email_and_this_afm_already_exists(self, afm, en_or_gr='en'):
        d = {self.e: f'This Vat code ({afm}) is already registered in another entry.',
             self.g: f'Αυτό το ΑΦΜ ({afm}) είναι ήδη καταχωρημένο σε άλλη εγγραφή.'}
        return d[en_or_gr]

    def user_with_other_email_and_this_pei_already_exists(self, pei, en_or_gr='en'):
        d = {self.e: f'This driver ID (ΠΕΙ no: {pei}) is already registered in another entry.',
             self.g: f'Αυτός ο αριθμός ΠΕΙ ({pei}) είναι ήδη καταχωρημένος σε άλλη εγγραφή.'}
        return d[en_or_gr]
