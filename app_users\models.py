from django.db import models
from django.contrib.auth.models import AbstractUser, Group, Permission
from django.utils import timezone

class User(AbstractUser):
    # Remove username field
    username = None

    # Use email as the unique identifier
    email = models.EmailField(unique=True)
    administrator_id = models.IntegerField()
    first_name = models.CharField(max_length=256, default='')
    last_name = models.Char<PERSON><PERSON>(max_length=256, default='')
    role = models.Cha<PERSON><PERSON><PERSON>(max_length=256, default='')
    is_active = models.<PERSON>oleanField(default=True)
    is_admin = models.BooleanField(default=False)
    language = models.Char<PERSON><PERSON>(max_length=8, default='el')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Set email as the login field
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []  # no other required fields for createsuperuser

    groups = models.ManyToManyField(
        Group,
        related_name='custom_user_groups',
        blank=True
    )
    user_permissions = models.ManyToMany<PERSON>ield(
        Permission,
        related_name='custom_user_permissions',
        blank=True
    )

    # This method might be causing the issue - it's generating a new administrator_id
    # Let's comment it out or modify it to be used only when needed
    @staticmethod
    def get_next_administrator_id():
        last_administrator = User.objects.order_by('administrator_id').last()
        if last_administrator:
            return last_administrator.administrator_id + 1
        return 0  # If does not exists last record then start from 0

    class Meta:
        ordering = ['created_at']
        constraints = [
            models.UniqueConstraint(fields=['email', 'administrator_id'], name='unique_email_per_admin')
        ]
        
    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"

class Token(models.Model):
    username = models.CharField(max_length=256)
    token = models.CharField(max_length=256)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Token for {self.username}"

    @staticmethod
    def get_next_administrator_id():
        last = User.objects.order_by('-administrator_id').first()
        return last.administrator_id + 1 if last else 0

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"

# class VerificationCode(models.Model):
#     email = models.CharField(max_length=64)
#     code = models.CharField(max_length=8)

#     created_at = models.DateTimeField(auto_now_add=True)
#     updated_at = models.DateTimeField(auto_now=True)

#     def __str__(self):
#         return f"Code for {self.email}"


# class LatestAppVersionUserNotification(models.Model):
#     user = models.ForeignKey(User, on_delete=models.CASCADE)
#     date_notification = models.DateTimeField(default=timezone.now)

#     created_at = models.DateTimeField(auto_now_add=True)

#     def __str__(self):
#         return f"Notification for {self.user.email} at {self.date_notification}"
