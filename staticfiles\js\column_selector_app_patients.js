document.addEventListener('DOMContentLoaded', function () {
    // Initialize all Offcanvas components safely
    document.querySelectorAll('.offcanvas').forEach(el => {
        bootstrap.Offcanvas.getOrCreateInstance(el);
    });

    // Handle column toggle checkboxes
    const columnToggles = document.querySelectorAll('.column-toggle');

    columnToggles.forEach(checkbox => {
        checkbox.addEventListener('change', function () {
            const tableId = this.dataset.tableId;
            const columnId = this.value;
            const isVisible = this.checked;

            // Show/hide corresponding columns
            const columns = document.querySelectorAll(`#${tableId} .column-${columnId}`);
            columns.forEach(column => {
                column.classList.toggle('d-none', !isVisible);
            });

            // Store updated visible columns in localStorage
            const visibleColumns = Array.from(document.querySelectorAll(`.column-toggle[data-table-id="${tableId}"]`))
                .filter(cb => cb.checked)
                .map(cb => cb.value);

            localStorage.setItem(`table_${tableId}_columns`, JSON.stringify(visibleColumns));
        });
    });

    // Load visibility preferences from localStorage on page load
    document.querySelectorAll('table[id]').forEach(table => {
        const tableId = table.id;
        const storedColumns = localStorage.getItem(`table_${tableId}_columns`);

        if (storedColumns) {
            const visibleColumns = JSON.parse(storedColumns);
            const checkboxes = document.querySelectorAll(`.column-toggle[data-table-id="${tableId}"]`);

            checkboxes.forEach(checkbox => {
                const columnId = checkbox.value;
                const isVisible = visibleColumns.includes(columnId);
                checkbox.checked = isVisible;

                const columns = document.querySelectorAll(`#${tableId} .column-${columnId}`);
                columns.forEach(column => {
                    column.classList.toggle('d-none', !isVisible);
                });
            });
        }
    });
});

document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.remove-column').forEach(btn => {
        btn.addEventListener('click', function () {
            const columnId = this.dataset.columnId;
            const tableId = this.dataset.tableId;

            // Hide the corresponding column
            document.querySelectorAll(`#${tableId} .column-${columnId}`).forEach(col => {
                col.classList.add('d-none');
            });

            // Uncheck the corresponding toggle (if any)
            const toggle = document.querySelector(`.column-toggle[data-table-id="${tableId}"][value="${columnId}"]`);
            if (toggle) {
                toggle.checked = false;
            }

            // Update localStorage
            const visibleColumns = Array.from(document.querySelectorAll(`.column-toggle[data-table-id="${tableId}"]`))
                .filter(cb => cb.checked)
                .map(cb => cb.value);

            localStorage.setItem(`table_${tableId}_columns`, JSON.stringify(visibleColumns));

            // Remove this row from the UI
            this.closest('.column-row').remove();
        });
    });
});
