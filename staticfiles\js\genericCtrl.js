document.addEventListener('DOMContentLoaded', function() {
    // Initialize column visibility from localStorage
    function initializeColumnVisibility(tableId) {
        const storedColumns = localStorage.getItem(`table_${tableId}_columns`);
        if (storedColumns) {
            const visibleColumns = JSON.parse(storedColumns);
            
            // Get all checkboxes for this table
            const checkboxes = document.querySelectorAll(`.column-toggle[data-table-id="${tableId}"]`);
            
            checkboxes.forEach(checkbox => {
                const columnId = checkbox.value;
                const isVisible = visibleColumns.includes(columnId);
                
                // Update checkbox state
                checkbox.checked = isVisible;
                
                // Update column visibility
                toggleColumnVisibility(tableId, columnId, isVisible);
            });
        }
    }

    // Toggle column visibility
    function toggleColumnVisibility(tableId, columnId, isVisible) {
        const columns = document.querySelectorAll(`#${tableId} .column-${columnId}`);
        columns.forEach(column => {
            if (isVisible) {
                column.classList.remove('d-none');
            } else {
                column.classList.add('d-none');
            }
        });
    }

    // Save column visibility state to localStorage
    function saveColumnVisibility(tableId) {
        const checkboxes = document.querySelectorAll(`.column-toggle[data-table-id="${tableId}"]`);
        const visibleColumns = Array.from(checkboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => checkbox.value);
        
        localStorage.setItem(`table_${tableId}_columns`, JSON.stringify(visibleColumns));
    }

    // Add event listeners to checkboxes
    function initializeColumnToggles() {
        document.querySelectorAll('.column-toggle').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const tableId = this.dataset.tableId;
                const columnId = this.value;
                const isVisible = this.checked;

                toggleColumnVisibility(tableId, columnId, isVisible);
                saveColumnVisibility(tableId);
            });
        });
    }

    // Initialize all tables on page load
    document.querySelectorAll('table[id]').forEach(table => {
        initializeColumnVisibility(table.id);
    });
    
    // Initialize column toggles
    initializeColumnToggles();
    
    // Re-initialize column toggles when dropdown is shown (in case of dynamic content)
    document.querySelectorAll('.dropdown-toggle').forEach(dropdown => {
        dropdown.addEventListener('shown.bs.dropdown', function() {
            initializeColumnToggles();
        });
    });

});

// Helper function to get CSRF token (same as before)
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            // Does this cookie string begin with the name we want?
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

let fileToDelete = {
    modelName: null,
    objectId: null,
    fieldName: null
};


