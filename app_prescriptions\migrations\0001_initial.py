# Generated by Django 5.2.1 on 2025-06-13 12:20

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('app_patients', '0009_rename_doctor_name_prescription_prescriber_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='Prescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(auto_now_add=True)),
                ('barcode_number', models.Char<PERSON>ield(max_length=256)),
                ('prescriber_name', models.Char<PERSON>ield(max_length=256)),
                ('pharmacy_name', models.Char<PERSON>ield(max_length=256)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prescriptions_from_prescriptions', to='app_patients.patient')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
