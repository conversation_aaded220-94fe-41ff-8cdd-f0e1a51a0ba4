// Function to open the delete modal and populate it with parameters
function openDeleteModal(tablename, id) {
  // Set modal content dynamically
  document.getElementById("tablename_delete_modal").value = tablename;
  document.getElementById("obj_id").value = id;
  // document.getElementById('description').textContent = description;

  console.log("deletemodal-->", tablename, " with id: ", id);

  // Use Bootstrap's modal API to show the modal
  const deleteModal = new bootstrap.Modal(
    document.getElementById("deleteModal")
  );
  deleteModal.show();
}

function openAddModal(modalId, modal_title, modal_description) {
  // Reset form
  const form = document.getElementById("add_form_id");

  if (form) form.reset();

  // Clear hidden ID field
  const idInput = document.getElementById("id_add");
  if (idInput) idInput.value = "";

  // Clear image previews
  const previews = document.querySelectorAll(`#${modalId} img[id^="preview"]`);
  previews.forEach((preview) => {
    preview.src = "";
    preview.style.display = "none";
  });

  // Show modal
  const modalElement = document.getElementById(modalId);
  if (modalElement) {
    try {
      const modal = new bootstrap.Modal(modalElement);
      modal.show();
    } catch (error) {
      console.error("Error showing modal:", error);
      console.log("Modal element exists:", !!modalElement);
      console.log("Bootstrap loaded:", typeof bootstrap !== "undefined");
      console.log(
        "Bootstrap Modal loaded:",
        typeof bootstrap !== "undefined" &&
          typeof bootstrap.Modal !== "undefined"
      );
    }
  } else {
    console.error(`Modal element with ID "${modalId}" not found`);
  }
}

/**
 * Opens the edit modal for a specific model
 * @param {string} modelName - The model name (e.g., 'Host')
 * @param {string} idField - The ID field name (usually 'id')
 * @param {string|number} idValue - The ID value
 * @param {string} modalId - The modal ID (e.g., 'editHostModal')
 */
function openEditModal(modelName, idField, idValue, modalId) {
  console.log(
    "openEditModal called with:",
    modelName,
    idField,
    idValue,
    modalId
  );

  // Find the modal element
  const modalElement = document.getElementById(modalId);
  if (!modalElement) {
    console.error(`Modal element with ID "${modalId}" not found`);
    return;
  }

  // Make the AJAX request to get the object data
  fetch(
    `/find_obj_with_id/?obj_id=${idValue}&tablename=${modelName}&fieldname=${idField}`
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.status === "success") {
        console.log("Data received:", data.data);

        // Special handling for Event model
        if (modelName === "Event") {
          console.log("Using special handling for Event model");

          // Make sure the modal is visible before populating
          const modal = new bootstrap.Modal(modalElement);
          modal.show();

          // Wait for modal to be fully shown
          modalElement.addEventListener(
            "shown.bs.modal",
            function onModalShown() {
              console.log("Modal shown, populating fields");
              populateEditEventModal(data.data);
              // Remove the event listener to avoid multiple calls
              modalElement.removeEventListener("shown.bs.modal", onModalShown);
            },
            { once: true }
          );
        } else {
          // Set ID field
          const idInput = document.getElementById("id_edit");
          if (idInput) {
            idInput.value = idValue;
          } else {
            console.error("ID input element not found");
          }

          // Set other fields dynamically
          for (const key in data.data) {
            const field = document.getElementById(`${key}_edit`);
            if (field) {
              console.log(`Setting field ${key}_edit to:`, data.data[key]);

              // Handle different field types
              if (field.type === "checkbox") {
                field.checked = Boolean(data.data[key]);
              } else if (field.type === "file") {
                // For file inputs, show preview if available
                if (data.data[key]) {
                  const previewImg = document.getElementById(
                    `preview${key}_edit`
                  );
                  if (previewImg) {
                    previewImg.src = data.data[key];
                    previewImg.style.display = "block";
                  }
                }
              } else if (field.type === "password") {
                // For password fields, leave them empty
                field.value = "";
                // Add a placeholder to indicate that leaving it empty will keep the existing password in italic
                field.placeholder =
                  "Αφήστε κενό για να διατηρήσετε τον υπάρχοντα κωδικό";
              } else {
                // For all other input types
                if (data.data[key] !== null && data.data[key] !== undefined) {
                  field.value = data.data[key];
                } else {
                  field.value = "";
                }
              }
            }
          }

          // Show the modal
          const modal = new bootstrap.Modal(modalElement);
          modal.show();
        }
      } else {
        console.error("Error fetching data:", data.message);
      }
    })
    .catch((error) => {
      console.error("Error:", error);
    });
}

function deleteFile1(modelName, objectId, fieldName) {
  console.log("Calling deleteFile...");
  console.log("modelName: " + modelName);
  console.log("objectId: " + objectId);
  console.log("fieldName: " + fieldName);

  // Store the parameters for later use
  fileToDelete.modelName = modelName;
  fileToDelete.objectId = objectId;
  fileToDelete.fieldName = fieldName;

  // Set modal content for file deletion
  // document.getElementById('value1').textContent = 'το αρχείο';
  document.getElementById("deleteFileModalLabel").textContent =
    "Διαγραφή Αρχείου";

  // Add a custom attribute to identify this as a file deletion
  document
    .getElementById("deleteFileModal")
    .setAttribute("data-delete-type", "file");

  // Show the existing delete modal
  const deleteFileModal = new bootstrap.Modal(
    document.getElementById("deleteFileModal")
  );
  deleteFileModal.show();
}

function uploadFile(modelName, objectId, fieldName, columnType) {
  console.log(
    "Uploading file for:",
    modelName,
    objectId,
    fieldName,
    columnType
  );
  const fileInput = document.createElement("input");
  fileInput.type = "file";
  fileInput.style.display = "none";
  document.body.appendChild(fileInput);

  fileInput.click();

  fileInput.onchange = function () {
    const file = fileInput.files[0];
    if (file) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("field_name", fieldName);

      // Define the allowed MIME types for each column type
      const allowedMimeTypes = {
        text: [],
        textarea: [],
        number: [],
        datetime: [],
        select: [],
        image: [
          "image/jpeg",
          "image/png",
          "image/gif",
          "image/bmp",
          "image/webp",
          "image/svg+xml",
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ],
        document: [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "application/vnd.oasis.opendocument.text",
          "application/vnd.oasis.opendocument.spreadsheet",
          "application/vnd.oasis.opendocument.presentation",
        ],
        // Add more column types and their allowed MIME types as needed
      };

      // Check the file type based on the column type
      if (
        allowedMimeTypes[columnType] &&
        !allowedMimeTypes[columnType].includes(file.type)
      ) {
        alert(`Please select a valid file for the ${fieldName} field.`);
        return;
      }

      fetch(`/api/${modelName}/${objectId}/upload_file/`, {
        method: "POST",
        body: formData,
        headers: {
          "X-CSRFToken": getCookie("csrftoken"),
        },
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            showSuccessToast("Το αρχείο ανέβηκε επιτυχώς!");
            location.reload();
          } else {
            showErrorToast("Error uploading file: " + data.error);
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          showErrorToast("An error occurred while uploading the file.");
        });
    }
    document.body.removeChild(fileInput);
  };
}

// Override the form submission in the delete modal
document.addEventListener("DOMContentLoaded", function () {
  const deleteForm = document.querySelector("#deleteFileModal form");
  if (deleteForm) {
    deleteForm.addEventListener("submit", function (e) {
      // Check if this is a file deletion
      const deleteType = document
        .getElementById("deleteFileModal")
        .getAttribute("data-delete-type");
      if (deleteType === "file") {
        e.preventDefault(); // Prevent the default form submission

        // Hide the modal
        const deleteModal = bootstrap.Modal.getInstance(
          document.getElementById("deleteFileModal")
        );
        deleteModal.hide();

        // Proceed with file deletion
        const formData = new FormData();
        formData.append("field_name", fileToDelete.fieldName);

        fetch(
          `/api/${fileToDelete.modelName}/${fileToDelete.objectId}/delete_file/`,
          {
            method: "POST",
            headers: {
              "X-CSRFToken": getCookie("csrftoken"),
            },
            body: formData,
          }
        )
          .then((response) => response.json())
          .then((data) => {
            if (data.success) {
              showSuccessToast("Το αρχείο διαγράφηκε επιτυχώς!");
              location.reload();
            } else {
              console.error("Error response:", data);
              showErrorToast(
                "Error deleting file: " + (data.error || "Unknown error")
              );
            }
          })
          .catch((error) => {
            console.error("Error:", error);
            showErrorToast("An error occurred while deleting the file.");
          });

        // Reset the delete type
        document
          .getElementById("deleteModal")
          .removeAttribute("data-delete-type");
      }
      // If not a file deletion, let the form submit normally
    });
  }
});

function togglePasswordVisibility(inputId, btn) {
  const input = document.getElementById(inputId);
  const icon = btn.querySelector("i");

  if (input.type === "password") {
    input.type = "text";
    icon.classList.remove("bi-eye-fill");
    icon.classList.add("bi-eye-slash-fill");
  } else {
    input.type = "password";
    icon.classList.remove("bi-eye-slash-fill");
    icon.classList.add("bi-eye-fill");
  }
}
function updateSelectField(selectElement) {
  const model = selectElement.dataset.model;
  const id = selectElement.dataset.id;
  const field = selectElement.dataset.field;
  const value = selectElement.value;

  // ✅ Update select class dynamically
  if(field === "is_active"){
    if (value === "True" || value === "true" || value === "1") {
      selectElement.classList.remove("bg-secondary", "text-white");
      selectElement.classList.add("bg-mint", "text-white");
    } else {
      selectElement.classList.remove("bg-mint");
      selectElement.classList.add("bg-secondary", "text-white");
    }
  }

  // Proceed with AJAX call
  fetch("/update_field/", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": document.querySelector("[name=csrfmiddlewaretoken]").value,
    },
    body: JSON.stringify({ model, id, field, value }),
  })
    .then((res) => res.json())
    .then((data) => console.log("✅ Updated:", data))
    .catch((error) => console.error("❌ Network error:", error));
}

// Simple email validation for modals
function setupEmailValidation() {
  // Handle modals when they're shown
  document.addEventListener(
    "shown.bs.modal",
    function (event) {
      const modal = event.target;

      // Find email inputs in this modal
      const emailInputs = modal.querySelectorAll(
        'input[type="email"], input[name*="email"], input[id*="email"]'
      );

      // Convert text inputs to email type if needed
      emailInputs.forEach((input) => {
        if (input.type !== "email") input.type = "email";

        // Add feedback element if missing
        const parent = input.parentNode;
        if (!parent.querySelector(".invalid-feedback")) {
          const feedback = document.createElement("div");
          feedback.className = "invalid-feedback";
          feedback.textContent = "Αυτό το email υπάρχει ήδη.";
          parent.appendChild(feedback);
        }

        // Add input validation with debounce
        input.addEventListener("input", debounceValidation);
      });

      // Set up form submission validation
      const form = modal.querySelector("form");
      if (form && emailInputs.length > 0) {
        form.addEventListener("submit", validateFormSubmission);
      }
    },
    true
  );

  // Also set up validation for any email inputs already in the page
  document
    .querySelectorAll(
      'input[type="email"], input[name*="email"], input[id*="email"]'
    )
    .forEach((input) => {
      if (input.type !== "email") input.type = "email";
      input.addEventListener("input", debounceValidation);

      // Add feedback element if missing
      const parent = input.parentNode;
      if (!parent.querySelector(".invalid-feedback")) {
        const feedback = document.createElement("div");
        feedback.className = "invalid-feedback";
        feedback.textContent = "Αυτό το email υπάρχει ήδη.";
        parent.appendChild(feedback);
      }
    });

  // Set up form submission validation for all forms
  document.querySelectorAll("form").forEach((form) => {
    form.addEventListener("submit", validateFormSubmission);
  });
}

// Debounced validation function
function debounceValidation() {
  clearTimeout(this.timer);
  this.timer = setTimeout(() => {
    const email = this.value.trim();
    if (email.length > 3 && email.includes("@")) {
      validateEmailWithServer(email, this);
    }
  }, 500);
}

// Server validation function
function validateEmailWithServer(email, inputElement) {
  const form = inputElement.closest("form");
  const model = form?.querySelector('[name="tablename"]')?.value || "";
  const objId = form?.querySelector('[name="id"]')?.value || "";

  const url = `/check_email_exists/?email=${encodeURIComponent(
    email
  )}&model=${model}&id=${objId}`;

  fetch(url)
    .then((response) => response.json())
    .then((data) => {
      inputElement.classList.toggle("is-invalid", data.exists);
    })
    .catch((err) => {
      console.error("Error checking email:", err);
    });
}

// Form submission validation
function validateFormSubmission(e) {
  const emailInput = this.querySelector('input[type="email"]');
  if (!emailInput) return;

  const email = emailInput.value.trim();
  if (email.length < 3) return;

  const form = this;
  const model = this.querySelector('[name="tablename"]')?.value || "";
  const objId = this.querySelector('[name="id"]')?.value || "";

  const url = `/check_email_exists/?email=${encodeURIComponent(
    email
  )}&model=${model}&id=${objId}`;

  // Prevent default to validate first
  e.preventDefault();

  fetch(url)
    .then((response) => response.json())
    .then((data) => {
      if (data.exists) {
        emailInput.classList.add("is-invalid");
      } else {
        emailInput.classList.remove("is-invalid");
        form.submit();
      }
    })
    .catch((err) => {
      console.error("Error checking email:", err);
      form.submit();
    });
}

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", setupEmailValidation);

document.addEventListener("DOMContentLoaded", function () {
  document.querySelectorAll(".modal").forEach((modal) => {
    modal.addEventListener("shown.bs.modal", () => {
      const form = modal.querySelector("form");
      const emailInput = form?.querySelector('input[type="email"]');
      if (!emailInput || !form) return;

      const modelInput = form.querySelector('[name="tablename"]');
      const idInput = form.querySelector('[name="id"]');

      // Add invalid-feedback if not already there
      let feedback =
        emailInput.parentElement.querySelector(".invalid-feedback");
      if (!feedback) {
        feedback = document.createElement("div");
        feedback.className = "invalid-feedback";
        feedback.textContent = "Αυτό το email υπάρχει ήδη.";
        emailInput.parentElement.appendChild(feedback);
      }

      // Debounced server check on input
      emailInput.addEventListener("input", function () {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
          const email = this.value.trim();
          if (email.length < 5 || !email.includes("@")) return;

          const model = modelInput?.value || "";
          const id = idInput?.value || "";

          fetch(
            `/check_email_exists/?email=${encodeURIComponent(
              email
            )}&model=${model}&id=${id}`
          )
            .then((res) => res.json())
            .then((data) => {
              if (data.exists) {
                emailInput.classList.add("is-invalid");
                emailInput.classList.remove("is-valid");
              } else {
                emailInput.classList.remove("is-invalid");
                emailInput.classList.add("is-valid");
              }
            });
        }, 400);
      });

      // Submit override
      form.addEventListener("submit", async function (e) {
        e.preventDefault();

        const email = emailInput.value.trim();
        const model = modelInput?.value || "";
        const id = idInput?.value || "";

        try {
          const response = await fetch(
            `/check_email_exists/?email=${encodeURIComponent(
              email
            )}&model=${model}&id=${id}`
          );
          const data = await response.json();

          if (data.exists) {
            emailInput.classList.add("is-invalid");
            emailInput.classList.remove("is-valid");
            return; // ❌ Block submit
          } else {
            emailInput.classList.remove("is-invalid");
            emailInput.classList.add("is-valid");

            // ✅ Finally submit the form
            this.submit();
          }
        } catch (error) {
          console.error("Email validation error:", error);
          showErrorToast("Αποτυχία ελέγχου email.");
        }
      });
    });
  });
});

document.addEventListener("DOMContentLoaded", function () {
  document.querySelectorAll("form[data-ajax-submit]").forEach((form) => {
    let isSubmitting = false;

    form.addEventListener("submit", function (e) {
      e.preventDefault(); // Prevent default form submission
      
      if (isSubmitting) {
        console.warn("⛔ Blocked duplicate submit");
        return;
      }

      isSubmitting = true;
      const submitButton = form.querySelector('button[type="submit"]');
      if (submitButton) {
        submitButton.disabled = true;
        submitButton.innerText = "Αποθήκευση...";
      }
      
      // Create FormData object from the form
      const formData = new FormData(form);
      
      // Send AJAX request
      fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => response.json())
      .then(data => {
        isSubmitting = false;
        if (submitButton) {
          submitButton.disabled = false;
          submitButton.innerText = "Αποθήκευση";
        }
        
        if (data.status === 'success') {
          // Show success message
          if (typeof showSuccessToast === 'function') {
            showSuccessToast(data.message);
          } else {
            // alert(data.message);
          }
          
          // Close any open modals
          const modal = bootstrap.Modal.getInstance(form.closest('.modal'));
          if (modal) {
            modal.hide();
          }
          
          // Redirect to users page
          window.location.href = '/users/';
        } else {
          // Show error message
          if (typeof showErrorToast === 'function') {
            showErrorToast(data.message);
          } else {
            // alert("Σφάλμα: " + data.message);
          }
          
          // If there's a specific field with error, highlight it
          if (data.field) {
            const fieldInput = form.querySelector(`[name="${data.field}"]`);
            if (fieldInput) {
              fieldInput.classList.add('is-invalid');
            }
          }
        }
      })
      .catch(error => {
        isSubmitting = false;
        if (submitButton) {
          submitButton.disabled = false;
          submitButton.innerText = "Αποθήκευση";
        }
        console.error("Error submitting form:", error);
        
        // Redirect to users page on error to avoid being stuck
        window.location.href = '/users/';
      });
    });
  });
});
