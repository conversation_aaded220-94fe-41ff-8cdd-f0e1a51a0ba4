# Generated by Django 5.2.1 on 2025-06-03 11:14

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Patient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('administrator_id', models.IntegerField()),
                ('amka', models.CharField(default='', max_length=20, unique=True)),
                ('first_name', models.Char<PERSON>ield(default='', max_length=50)),
                ('last_name', models.Char<PERSON><PERSON>(default='', max_length=50, unique=True)),
                ('gender', models.Char<PERSON><PERSON>(default='', max_length=20)),
                ('father_name', models.CharField(default='', max_length=50)),
                ('date_of_birth', models.DateField(blank=True, null=True)),
                ('recommendation', models.Char<PERSON>ield(default='')),
                ('contract', models.Char<PERSON>ield(default='', max_length=50)),
                ('dressing', models.Char<PERSON>ield(default='', max_length=50)),
                ('address', models.TextField(default='')),
                ('area', models.CharField(default='', max_length=256)),
                ('floor', models.CharField(default='', max_length=256)),
                ('bell', models.CharField(default='', max_length=256)),
                ('postal_code', models.CharField(default='', max_length=20)),
                ('city', models.CharField(default='', max_length=256)),
                ('medical_condition', models.CharField(default='', max_length=256)),
                ('phone', models.CharField(default='', max_length=256)),
                ('email', models.EmailField(default='', max_length=254, unique=True)),
                ('icd10_code', models.CharField(default='', max_length=256)),
                ('doctor_name', models.CharField(default='', max_length=256)),
                ('doctor_phone', models.CharField(default='', max_length=256)),
                ('doctor_email', models.EmailField(default='', max_length=254, unique=True)),
                ('eprescription', models.CharField(default='', max_length=256)),
                ('kepa_decision_number', models.CharField(default='', max_length=256)),
                ('kepa_decision_date', models.DateField(blank=True, null=True)),
                ('insurance_provider', models.CharField(default='', max_length=256)),
                ('serving_pharmacy', models.CharField(default='', max_length=256)),
                ('prescriber_name', models.CharField(default='', max_length=256)),
                ('prescriber_phone', models.CharField(default='', max_length=256)),
                ('prescriber_email', models.EmailField(default='', max_length=254, unique=True)),
                ('patient_history', models.TextField(default='')),
                ('financial_offers', models.TextField(default='')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
    ]
