{% extends "base_auth.html" %}
{% load static %}
{% block css %}
    <link rel="stylesheet" href="{% static 'css/login.css' %}">
{% endblock css %}
{% block title %}
    {% if forgot_password_step == 1 %}
        Forgot Password
    {% elif forgot_password_step == 2 %}
        Reset Password
    {% endif %}
{% endblock %}
{% block content %}
    <div class="container py-4 ">
        <div class="row justify-content-center">
            <div class="col-12 col-md-12 col-lg-11">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-4">
                        <h4 class="text-center mb-4 fw-semibold text-secondary">
                            {% if forgot_password_step == 1 %}
                                Ξεχάσατε τον κωδικό σας;
                            {% else %}
                                Επαναφορά Κωδικού
                            {% endif %}
                        </h4>
                        {% if forgot_password_step == 1 %}
                            <p class="text-center">Πληκτρολογήστε το email σας και θα σας στείλουμε έναν κωδικό επαλήθευσης.</p>
                            <form method="post"
                                  action="{% url 'forgot_password_send_code_to_email_go_to_step_2' %}">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="email" class="form-label fw-medium text-secondary text-start">Email</label>
                                    <input type="email"
                                           class="form-control form-control-lg"
                                           id="email"
                                           name="email"
                                           value="{{ email }}"
                                           required>
                                </div>
                                {% if error_message %}<div class="alert alert-danger py-2">{{ error_message }}</div>{% endif %}
                                {% if success_message %}<div class="alert alert-success py-2">{{ success_message }}</div>{% endif %}
                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-dark btn-lg">Αποστολή Κωδικού</button>
                                    <a href="{% url 'login' %}" class="btn btn-outline-dark btn-lg">Πίσω στη Σύνδεση</a>
                                </div>
                            </form>
                        {% elif forgot_password_step == 2 %}
                            <p class="text-center">Πληκτρολογήστε τον κωδικό επαλήθευσης και τον νέο κωδικό σας.</p>
                            <form method="post"
                                  action="{% url 'forgot-password-change-password-step-2' %}">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="email_id" class="form-label fw-medium text-secondary">Email</label>
                                    <input type="email"
                                           class="form-control form-control-lg"
                                           id="email_id"
                                           name="email"
                                           value="{{ email }}"
                                           readonly>
                                </div>
                                <div class="mb-3">
                                    <label for="code_id" class="form-label fw-medium text-secondary">Kωδικός Επαλήθευσης</label>
                                    <input type="text"
                                           class="form-control form-control-lg"
                                           id="code_id"
                                           name="code"
                                           required>
                                </div>
                                <div class="mb-3">
                                    <label for="new_password_1_id" class="form-label fw-medium text-secondary">Νέος Κωδικός</label>
                                    <input type="password"
                                           class="form-control form-control-lg"
                                           id="new_password_1_id"
                                           name="password"
                                           required>
                                </div>
                                <div class="mb-3">
                                    <label for="new_password_2_id" class="form-label fw-medium text-secondary">Επιβεβαίωση Κωδικού</label>
                                    <input type="password"
                                           class="form-control form-control-lg"
                                           id="new_password_2_id"
                                           name="new_password_2"
                                           required>
                                </div>
                                {% if error_message %}<div class="alert alert-danger py-2">{{ error_message }}</div>{% endif %}
                                {% if success_message %}<div class="alert alert-success py-2">{{ success_message }}</div>{% endif %}
                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-dark btn-lg">Επαναφορά Κωδικού</button>
                                    <a href="{% url 'login' %}" class="btn btn-outline-dark btn-lg">Πίσω στη Σύνδεση</a>
                                </div>
                            </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
