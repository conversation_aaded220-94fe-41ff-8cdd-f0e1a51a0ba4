{% load static %}
{% load custom_filters %}
<div class="row">
    <!-- Left: Ιατρικές Οδηγίες -->
    <div class="col-md-7">
        <div style="max-height: 60vh; overflow-y: auto; padding-right: 10px">
            {% for note in notes %}
                <div class="card mb-3 border-top-5 overflow-y-auto"
                     style="border-top: 10px solid #6AC7BA">
                    <div class="card-header d-flex justify-content-between align-items-center"
                         style="background-color: white">
                        <span class="fw-bold">{{ note.created_at|format_date_gr }}</span>
                        <form method="POST"
                              action="{% url 'delete_medical_note' patient.id note.id %}"
                              style="margin: 0">
                            {% csrf_token %}
                            <button type="button"
                                    class="edit-note-btn btn delete-edit-btn rounded-pill text-center d-inline-flex align-items-center justify-content-center edit-note-btn"
                                    data-bs-toggle="modal"
                                    data-bs-target="#medicalNoteModal"
                                    data-note-id="{{ note.id }}"
                                    data-note-text="{{ note.note|escapejs }}">
                                <i class="bi bi-pencil-fill"></i>
                            </button>
                            <button type="submit"
                                    class="btn delete-edit-btn rounded-pill text-center d-inline-flex align-items-center justify-content-center">
                                <i class="bi bi-trash-fill"></i>
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        <p class="fst-italic">{{ note.note }}</p>
                    </div>
                </div>
            {% empty %}
                <p>Δεν υπάρχουν ακόμη ιατρικές οδηγίες.</p>
            {% endfor %}
        </div>
    </div>
    <!-- Right: Εικόνα + Κουμπί -->
    <div class="col-md-5 d-flex flex-column align-items-center justify-content-center">
        <img src="{% static 'images/medical_notes.png' %}"
             alt="Ιατρικές Οδηγίες"
             class="img-fluid mb-3"
             style="max-height: 320px;
                    object-fit: contain">
        <button class="btn medical_note_btn px-4 py-3 mt-4"
                data-bs-toggle="modal"
                data-bs-target="#medicalNoteModal">
            Νέα Ιατρική Συμβουλή <i class="bi bi-plus-circle ms-1"></i>
        </button>
    </div>
</div>
<!-- Modal -->
<div class="modal fade"
     id="medicalNoteModal"
     tabindex="-1"
     aria-labelledby="medicalNoteModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <form method="POST"
              id="medicalNoteForm"
              class="w-100"
              data-patient-id="{{ patient.id }}"
              data-create-url="{% url 'patient_tab_content' patient.id 'medical_notes' %}"
              data-update-template-url="{% url 'update_medical_note' patient.id 0 %}">
            {% csrf_token %}
            <div class="modal-content p-2">
                <div class="modal-header">
                    <h5 class="modal-title">Ιατρική Συμβουλή</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <textarea name="note"
                              id="note-textarea"
                              class="form-control"
                              rows="5"
                              placeholder="Πληκτρολογήστε μία νέα ιατρική συμβουλή..."></textarea>
                </div>
                <div class="modal-footer border-0">
                    <button type="submit" class="btn btn-dark rounded-pill px-4">Αποθήκευση</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const modal = document.getElementById('medicalNoteModal');
        const form = document.getElementById('medicalNoteForm');
        const textarea = modal.querySelector('#note-textarea');
        const createUrl = form.dataset.createUrl;
        const updateUrlTemplate = form.dataset.updateTemplateUrl;

        modal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;

            if (button && button.classList.contains('edit-note-btn')) {
                const noteId = button.getAttribute('data-note-id');
                const noteText = button.getAttribute('data-note-text');

                textarea.value = noteText || '';
                form.action = updateUrlTemplate.replace('/0/', `/${noteId}/`);
            } else {
                textarea.value = '';
                form.action = createUrl;
            }
        });
    });
</script>
