
ADD_MODAL_ID = 'addModalId'
EDIT_MODAL_ID = 'editModalId'

COLUMNS = [
    {
        'id': 'id',
        'label': '#',
        'field': 'id',
        'type': 'counter',
        'required': True,
        'default_visible': True
    },
   
    {
        'id': 'name',
        'label': 'ΟΝΟΜΑ',
        'field': 'name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    # {
    #     'id': 'message',
    #     'label': 'ΜΗΝΥΜΑ',
    #     'field': 'message',
    #     'type': 'text',
    #     'required': False,
    #     'default_visible': True
    # },
     {
        'id': 'type',
        'label': 'ΤΥΠΟΣ',
        'field': 'type',
        'type': 'text',
        'required': False,
        'default_visible': True,
        'options': [
            {'value': 'email', 'label': 'Email'},
            {'value': 'sms', 'label': 'SMS'}
        ]

    },
    {
        'id': 'is_active',
        'label': 'ΚΑΤΑΣΤΑΣΗ',
        'field': 'is_active',
        'type': 'status',
        'required': False,
        'default_visible': True,
        'options': [
             {'value': True, 'label': 'Ενεργοποιμένο'},
             {'value': False, 'label': 'Μη Ενεργοποιμένο'}
        ]
    },
    {
        'id': 'actions',
        'label': 'ΕΝΕΡΓΕΙΕΣ',
        'type': 'actions',
        'required': True,
        'default_visible': True
    }

]

FILTERS = [
    {
        'id': 'order_by',
        'name': 'order_by',
        'label': 'Ταξινόμηση',
        'type': 'select',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
            },
        'options': [
            {'value': 'name__asc', 'label': 'Όνομα Αύξουσα ↑'},
            {'value': 'name__desc', 'label': 'Όνομα Φθίνουσα ↓'}
        ]
    },
    {
        'id': 'type_filter',
        'name': 'type__contains',
        'label': 'Τύπος',
        'type': 'select',
        'width': '150px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
        },
        'options': [
            {'value': 'email', 'label': 'Email'},
            {'value': 'sms', 'label': 'SMS'}
        ]
    },

    {
        'id': 'name_filter',
        'name': 'name__contains',
        'label': 'Όνομα',
        'type': 'text',
        'width': '150px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary rounded-pill'
        }
    },
    {
        'id': 'is_active_filter',
        'name': 'is_active',
        'label': 'Κατάσταση',
        'type': 'select',
        'width': '160px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
        },
        'options': [
            {'value': 'True', 'label': 'Ενεργοποιημένο'},
            {'value': 'False', 'label': 'Μη Ενεργοποιημένο'}
        ]
    }

]

FORM_FIELDS = [
    {
        'id': 'type',
        'label': 'Τύπος',
        'type': 'select',
        'required': True,
        'width': '12',
        'options': [
            {'value': 'email', 'label': 'Email'},
            {'value': 'sms', 'label': 'SMS'}
        ]
    },
    {
        'id': 'name',
        'label': 'Όνομα',
        'type': 'text',
        'required': True,
        'width': '12',
        'placeholder': 'Εισάγετε όνομα'
    },
    {
        'id': 'message',
        'label': 'Μήνυμα',
        'type': 'richtext',
        'required': True,
        'width': '12',
        'placeholder': 'Εισάγετε το μήνυμα σας..'
    }

]





