    
// Function to open the delete modal and populate it with parameters
/**
 * Opens the edit modal for a specific model
 * @param {string} modelName - The model name (e.g., 'Host')
 * @param {string} idField - The ID field name (usually 'id')
 * @param {string|number} idValue - The ID value
 * @param {string} modalId - The modal ID (e.g., 'editHostModal')
 */

function openDeleteModal(tablename, id) {
    // Set modal content dynamically
    document.getElementById('tablename_delete_modal').value = tablename;
    document.getElementById('obj_id').value = id;
    // document.getElementById('description').textContent = description;

    console.log("deletemodal-->", tablename, " with id: ", id);

    // Use Bootstrap's modal API to show the modal
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

function openAddModal(modalId, modal_title, modal_description) {        
    // Reset form
    const form = document.getElementById('add_form_id');

    if (form) form.reset();
    
    // Clear hidden ID field
    const idInput = document.getElementById('id_add');
    if (idInput) idInput.value = '';
    
    // Clear image previews
    const previews = document.querySelectorAll(`#${modalId} img[id^="preview"]`);
    previews.forEach(preview => {
        preview.src = '';
        preview.style.display = 'none';
    });
    
    // Show modal
    const modalElement = document.getElementById(modalId);
    if (modalElement) {
        try {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } catch (error) {
            console.error('Error showing modal:', error);
            console.log('Modal element exists:', !!modalElement);
            console.log('Bootstrap loaded:', typeof bootstrap !== 'undefined');
            console.log('Bootstrap Modal loaded:', typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined');
        }
    } else {
        console.error(`Modal element with ID "${modalId}" not found`);
    }
}



function openEditModal(modelName, idField, idValue, modalId) {
    console.log('openEditModal called with:', modelName, idField, idValue, modalId);
    
    // Find the modal element
    const modalElement = document.getElementById(modalId);
    if (!modalElement) {
        console.error(`Modal element with ID "${modalId}" not found`);
        return;
    }
    
    // Make the AJAX request to get the object data
    fetch(`/find_obj_with_id/?obj_id=${idValue}&tablename=${modelName}&fieldname=${idField}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('Data received:', data.data);
                
                // Special handling for Event model
                if (modelName === 'Event') {
                    console.log('Using special handling for Event model');
                    
                    // Make sure the modal is visible before populating
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                    
                    // Wait for modal to be fully shown
                    modalElement.addEventListener('shown.bs.modal', function onModalShown() {
                        console.log('Modal shown, populating fields');
                        populateEditEventModal(data.data);
                        // Remove the event listener to avoid multiple calls
                        modalElement.removeEventListener('shown.bs.modal', onModalShown);
                    }, { once: true });
                } else {
                    // Set ID field
                    const idInput = document.getElementById('id_edit');
                    if (idInput) {
                        idInput.value = idValue;
                    } else {
                        console.error('ID input element not found');
                    }
                    
                    // Set other fields dynamically
                    for (const key in data.data) {
                        const field = document.getElementById(`${key}_edit`);
                        if (field) {
                            console.log(`Setting field ${key}_edit to:`, data.data[key]);
                            
                            // Handle different field types
                            if (field.type === 'checkbox') {
                                field.checked = Boolean(data.data[key]);
                            } else if (field.type === 'file') {
                                // For file inputs, show preview if available
                                if (data.data[key]) {
                                    const previewImg = document.getElementById(`preview${key}_edit`);
                                    if (previewImg) {
                                        previewImg.src = data.data[key];
                                        previewImg.style.display = 'block';
                                    }
                                }
                            } else if (field.type === 'password') {
                                // For password fields, leave them empty
                                field.value = '';
                                // Add a placeholder to indicate that leaving it empty will keep the existing password in italic
                                field.placeholder = 'Αφήστε κενό για να διατηρήσετε τον υπάρχοντα κωδικό';
                           } else {
                                if (data.data[key] !== null && data.data[key] !== undefined) {
                                    field.value = data.data[key];
                                } else {
                                    field.value = '';
                                }

                                // ✅ Set Quill editor content if applicable
                                const editorEntry = editors.find(e => e.fieldId === `${key}_edit`);
                                if (editorEntry && editorEntry.quill) {
                                    editorEntry.quill.root.innerHTML = data.data[key] || '';
                                }
                            }

                        }
                    }
                    
                    // Show the modal
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                }
            } else {
                console.error('Error fetching data:', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function updateTextareaField(textarea) {
    const model = textarea.dataset.model;
    const id = textarea.dataset.id;
    const field = textarea.dataset.field;
    const value = textarea.value;
    

    fetch(`/update-field/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken'),
        },
        body: JSON.stringify({
            model: model,
            id: id,
            field: field,
            value: value,
        })
    }).then(response => {
        if (!response.ok) {
            alert("Failed to save.");
        }
    });
}

function updateSelectField(selectElement) {
    const model = selectElement.dataset.model;
    const id = selectElement.dataset.id;
    const field = selectElement.dataset.field;
    const value = selectElement.value;

    // Clear existing style classes
    selectElement.classList.remove('bg-mint', 'bg-gray', 'text-white', 'text-bold');

    // Re-apply based on value
    if (value === "True" || value === "true" || value === "1") {
        selectElement.classList.add('bg-mint', 'text-white', 'text-bold');
    } else {
        selectElement.classList.add('bg-gray', 'text-white');
    }

    // Send update to backend
    fetch('/update_field/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({ model, id, field, value })
    })
    .then(res => res.json())
    .then(data => console.log('✅ Updated:', data))
    .catch(error => console.error('❌ Network error:', error));
}

// Declare globally so other functions like openEditModal can access it
const editors = [];

document.addEventListener("DOMContentLoaded", function () {
    // Loop through all Quill containers
    document.querySelectorAll('.quill-container').forEach(function (container) {
        const fieldId = container.dataset.fieldId;
        const formId = container.dataset.formId;

        const quill = new Quill(`#${container.id}`, {
            theme: "snow"
        });

        editors.push({ fieldId, formId, quill });
    });

    // Attach submit handlers once per form
    const handledForms = new Set();

    editors.forEach(({ fieldId, formId, quill }) => {
        const form = document.getElementById(formId);
        if (form && !handledForms.has(formId)) {
            form.addEventListener("submit", function () {
                editors.forEach(({ fieldId, formId: fId, quill }) => {
                    if (fId === formId) {
                        const html = quill.root.innerHTML;
                        const input = document.getElementById(fieldId);
                        if (input) input.value = html;
                    }
                });
            });
            handledForms.add(formId);
        }
    });
});

