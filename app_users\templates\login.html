{% extends "base_auth.html" %}
{% load static %}
{% block css %}
    <link rel="stylesheet" href="{% static 'css/login.css' %}">
{% endblock css %}
{% block title %}Login{% endblock %}
{% block content %}
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12 col-md-10 col-lg-10">
                <div class="card shadow-sm border-0 mt-4">
                    <div class="card-body p-4">
                        <h4 class="text-center mb-4 fw-semibold text-secondary">Σύνδεση</h4>
                        <form method="POST" action="{% url 'login' %}">
                            {% csrf_token %}
                            <input type="hidden" name="next" value="{{ request.GET.next }}">
                            <div class="mb-3">
                                <label for="email" class="form-label fw-medium text-secondary">Email</label>
                                <input type="email"
                                       class="form-control form-control-lg"
                                       id="email"
                                       name="email"
                                       placeholder="<EMAIL>"
                                       required
                                       autocomplete="on">
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label fw-medium text-secondary">Κωδικός</label>
                                <div style="position: relative; width: 100%;">
                                    <input type="password"
                                           class="form-control form-control-lg"
                                           id="password"
                                           name="password"
                                           required
                                           style="padding-right: 30px">
                                    <span id="toggle-password"
                                          class="text-forget-password fst-italic small"
                                          style="cursor: pointer;
                                                 position: absolute;
                                                 right: 10px;
                                                 top: 50%;
                                                 transform: translateY(-50%)">👁️</span>
                                </div>
                                {% if message %}<div class="alert alert-danger py-2" role="alert">{{ message }}</div>{% endif %}
                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-dark btn-lg custom-btn">Σύνδεση</button>
                                    <!-- <a href="/register/" class="btn btn-outline-dark btn-lg custom-btn">Εγγραφή</a> -->
                                </div>
                                <!-- <div class="text-center mt-4">
                                    <a href="/forgot_password/" class="text-forget-password fst-italic medium">Ξέχασα τον κωδικό μου</a>
                                </div> -->
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script>
    document.addEventListener('DOMContentLoaded', function () {
        const togglePassword = document.getElementById('toggle-password');
        const passwordInput = document.getElementById('password');

        togglePassword.addEventListener('click', function () {
            // Toggle the password visibility
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Change the eye icon
            this.textContent = type === 'password' ? '👁️' : '🙈';
        });
    });
        </script>
    {% endblock %}
