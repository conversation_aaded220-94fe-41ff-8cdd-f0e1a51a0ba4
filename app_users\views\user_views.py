from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Case, When, Value, IntegerField
from app_users.models import User
from utilities.services_app import prepare_filters_and_context, prepare_order, pagination
from app_users.table_configs.tconf_app_users import FILTERS, COLUMNS, FORM_FIELDS, ADD_MODAL_ID, EDIT_MODAL_ID
import traceback
from utilities import messages as custom_messages  # ✅ your Messages class
from utilities import parameters as params
from utilities.services_app import createOrUpdate
from utilities.logs_manager import LogsManager
from django.contrib import messages
from django.shortcuts import redirect
from django.shortcuts import get_object_or_404
from django.contrib import messages as django_messages
from utilities import logs_manager, utils
from django.db.utils import IntegrityError
from django.http import JsonResponse


logs_manager = LogsManager(params.logs_dir)
messages_obj = custom_messages.Messages()


@login_required
def get_users(request):
    try:
        administrator_id = request.user.administrator_id
        users = User.objects.filter(administrator_id=administrator_id)

        query_params = request.GET.dict()
        filters, context_filters, custom_q_filters = prepare_filters_and_context(query_params, "User")

        order_by_param = request.GET.get('order_by', '')
        if order_by_param:
            ordering = prepare_order(request, order_by_param)
            field, _ = order_by_param.split('__')
            users = users.filter(**filters).annotate(
                null_priority=Case(
                    When(**{f"{field}__isnull": True}, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField()
                )
            ).order_by('null_priority', ordering)
        else:
            users = users.filter(**filters)

        context = {
            'page_obj': pagination(request, users, 30),
            'order_by': order_by_param,
            **context_filters,
            'filters': FILTERS,
            'columns': COLUMNS,
            'form_fields': FORM_FIELDS,
            'addModalId': ADD_MODAL_ID,
            'editModalId': EDIT_MODAL_ID,
            'add_button_label': 'Νέα Καταχώρηση',
            'add_modal_title': 'Νέα Καταχώρηση Χρήστη',
            'edit_modal_title': 'Επεξεργασία Χρήστη',
            'modal_description': '',
            'model_name': 'User',
            'page_title': 'Χρήστες',
            'table_id': 'users_table',
            'get_action': 'get_users',
            'modal_delete_obj_description': '',
            'create_update_url': 'createUpdateUser',  # Use the URL name as a string, not the result of reverse()
            'delete_url': 'delete_user'  # Use the URL name as a string, not the result of reverse()
        }

        return render(request, 'users.html', context)

    except Exception as e:
        traceback.print_exc()
        return render(request, 'users.html', {
            'error_message': f"Σφάλμα: {str(e)}"
        })


@login_required
def createUpdateUser(request):
    try:
        message = ''
        if request.method == 'POST':
            print("REQUEST PATH: " + request.path)
            
            # Get all form data
            form_data = {
                key: value.strip() if isinstance(value, str) else value
                for key, value in request.POST.items()
            }

            # Handle file uploads
            for key, file in request.FILES.items():
                form_data[key] = file

            email = form_data.get('email', '').strip()
            obj_id = form_data.get('id', '').strip()
            password = form_data.get('password', '').strip()
            current_admin_id = request.user.administrator_id

            # ✅ EMAIL UNIQUENESS CHECK
            if email:
                qs = User.objects.filter(email=email)
                if obj_id:
                    qs = qs.exclude(id=obj_id)
                if qs.exists():
                    message = "Αυτό το email υπάρχει ήδη."
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({'status': 'error', 'message': message, 'field': 'email'}, status=400)
                    else:
                        messages.error(request, message)
                        return render(request, 'users.html', {'error_message': message})

            # ✅ CREATE OR UPDATE
            if obj_id:
                # Update
                user = User.objects.get(id=obj_id)
                for key, value in form_data.items():
                    if key not in ['id', 'password', 'csrfmiddlewaretoken'] and hasattr(user, key):
                        setattr(user, key, value)
                if password:
                    user.set_password(password)
                user.save()
                message = "Ο χρήστης ενημερώθηκε με επιτυχία!"
            else:
                # Create
                user = User(
                    email=email,
                    is_admin=form_data.get('is_admin') == 'True',
                    is_active=form_data.get('is_active', 'True') == 'True',
                    administrator_id=current_admin_id,
                    first_name=form_data.get('first_name', ''),
                    last_name=form_data.get('last_name', ''),
                    role=form_data.get('role', '')
                )
                if password:
                    user.set_password(password)
                
                temp_user = User.objects.filter(email=email)
                print("temp_user: " + str(temp_user))
                if temp_user.exists():
                    print("temp_user exists")
                    message = "Ο χρήστης με το email υπάρχει ήδη!"
                    messages.error(request, message)
                    return redirect('get_users')
                user.save()
                message = "Ο χρήστης δημιουργήθηκε με επιτυχία!"

            # ✅ SUCCESS RESPONSE
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'status': 'success', 'message': message})
            else:
                messages.success(request, message)
                return redirect('get_users')

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)  # Add this line to return JsonResponse


@login_required
def delete_user(request):
    model_class = User
    try:
        if request.method == 'POST':
            obj_id = request.POST.get('obj_id')
            print("delete_user with id: " + obj_id)

            if not obj_id:
                django_messages.error(request, "Δεν δόθηκε έγκυρο ID για διαγραφή.")
                return redirect('get_users')

            obj = get_object_or_404(model_class, id=obj_id)
            print(obj)

            # ✅ Διαγραφή logo αρχείου αν υπάρχει
            # if obj.logo and hasattr(obj.logo, 'url'):
            #     from utils.file_utils import delete_file_by_url
            #     delete_file_by_url(obj.logo.url)

            obj.delete()
            django_messages.success(request, "Το αντικείμενο διαγράφηκε με επιτυχία!")
        else:
            django_messages.error(request, "Μη υποστηριζόμενη μέθοδος.")

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        django_messages.error(request, messages_obj.exception(e, params.default_language))

    return redirect('get_users') 
