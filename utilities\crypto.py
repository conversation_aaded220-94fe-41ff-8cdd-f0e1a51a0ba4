import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import os
import sys
import secrets
import random

current_dir = os.getcwd()
sys.path.append(current_dir)
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
# sys.path.append('/opt/smart_college')


from local_settings import k


class Crypto:
    KDF_ALGORITHM = hashes.SHA256()
    KDF_LENGTH = 32
    KDF_ITERATIONS = 140000
    # P = k.p
    # Salt = parms.S_16bytes
    # Salt = k.S_32bytes

    def __init__(self):
        a = None
        # self.initialize_django_k_local_settings()

    def encrypt_str(self, text, password, salt) -> str:
        e, _ = self.encrypt(text, password=password, salt=salt)
        e_dec = e.decode()
        # print(e_dec)
        return e_dec

    def decrypt_str(self, encrypted_str, password, salt) -> str:
        encrypted_binary = encrypted_str.encode('utf8')
        d = self.decrypt(encrypted_binary, password=password, salt=salt)
        return d

    def encrypt(self, plaintext: str, password: str, salt=None) -> (bytes, bytes):
        # Derive a symmetric key using the passsword and a fresh random salt.
        if not salt:
            salt = secrets.token_bytes(32)
        kdf = PBKDF2HMAC(
            algorithm=self.KDF_ALGORITHM, length=self.KDF_LENGTH, salt=salt,
            iterations=self.KDF_ITERATIONS)
        key = kdf.derive(password.encode("utf-8"))
        # Encrypt the message.
        f = Fernet(base64.urlsafe_b64encode(key))
        ciphertext = f.encrypt(plaintext.encode("utf-8"))
        return ciphertext, salt

    def decrypt(self, ciphertext: bytes, password: str, salt: bytes) -> str:
        # try:
        # Derive the symmetric key using the password and provided salt.
        kdf = PBKDF2HMAC(
            algorithm=self.KDF_ALGORITHM, length=self.KDF_LENGTH, salt=salt,
            iterations=self.KDF_ITERATIONS)
        key = kdf.derive(password.encode("utf-8"))
        # Decrypt the message
        f = Fernet(base64.urlsafe_b64encode(key))
        plaintext = f.decrypt(ciphertext)
        return plaintext.decode("utf-8")

    def get_salt(self, s_bytes=32):
        # salt = secrets.token_urlsafe(bytes)
        # print(salt)
        return secrets.token_bytes(s_bytes)
        # print(salt)



    # It is used for encrypted and dectrypted of string type
    def encrypt_decrypt_test2(self, message):
        # message = 'message for encryption'
        encrypted = self.encrypt_str(message, k.p, k.S_32bytes)
        decrypted = self.decrypt_str(encrypted, k.p, k.S_32bytes)
        print(f"message: {message}")
        print(f"encrypted: {len(encrypted)} {encrypted}")
        print(f"decrypted: {decrypted}")

    # run this method to initialize django keys
    def initialize_django_k_local_settings(self, p_len=50, s_bytes=32, settings_k_len=92, token_without_login_len=64):
        used_str = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!"#$%&()*+,-./:;<=>?@[]^_|~0123456789'
        len_of_used_str= len(used_str)

        # get p
        p = ''
        for i in range(0, p_len):
            pos = random.randint(0, len_of_used_str - 1)
            p += used_str[pos]
        print(f'p: {p}')

        # get s
        s = secrets.token_bytes(s_bytes)
        # s = secrets.token_urlsafe(s_bytes)
        print(f's_{s_bytes}bytes: {s}')

        # settings_k
        settings_k = ''
        for i in range(0, settings_k_len):
            pos = random.randint(0, len_of_used_str - 1)
            settings_k += used_str[pos]
        print(f'settings_k: {settings_k}')

        # encr_settings_k
        encr_settings_k = self.encrypt_str(settings_k, p, s)
        print(f'encr_settings_k: {encr_settings_k}')

        # token_without_login
        # token_without_login = secrets.token_urlsafe(token_without_login_len)
        token_without_login = ''
        for i in range(0, token_without_login_len):
            used_str = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!"#$%&()*+,-./:;<=>?@[]^_|~0123456789'
            pos = random.randint(0, len_of_used_str - 1)
            token_without_login += used_str[pos]
        print(f'token_without_login: {token_without_login}')

        # encr_settings_k
        encr_token_without_login = self.encrypt_str(token_without_login, p, s)
        print(f'encr_token_without_login: {encr_token_without_login}')


def get_decrypted_str_used_in_django_settings():
    c = Crypto()
    return c.decrypt_str(k.encr_settings_k, k.p, k.S_32bytes)





# if __name__ == "__main__":
#        Crypto()












