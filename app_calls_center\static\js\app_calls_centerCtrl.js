// Function to open the delete modal and populate it with parameters
function openDeleteModal(tablename, id) {
  // Set modal content dynamically
  document.getElementById("tablename_delete_modal").value = tablename;
  document.getElementById("obj_id").value = id;
  // document.getElementById('description').textContent = description;

  console.log("deletemodal-->", tablename, " with id: ", id);

  // Use Bootstrap's modal API to show the modal
  const deleteModal = new bootstrap.Modal(
    document.getElementById("deleteModal")
  );
  deleteModal.show();
}

function openAddModal(modalId, modal_title, modal_description) {
  // Reset form
  const form = document.getElementById("add_form_id");

  if (form) form.reset();

  // Clear hidden ID field
  const idInput = document.getElementById("id_add");
  if (idInput) idInput.value = "";

  // Clear image previews
  const previews = document.querySelectorAll(`#${modalId} img[id^="preview"]`);
  previews.forEach((preview) => {
    preview.src = "";
    preview.style.display = "none";
  });

  // Show modal
  const modalElement = document.getElementById(modalId);
  if (modalElement) {
    try {
      const modal = new bootstrap.Modal(modalElement);
      modal.show();
    } catch (error) {
      console.error("Error showing modal:", error);
      console.log("Modal element exists:", !!modalElement);
      console.log("Bootstrap loaded:", typeof bootstrap !== "undefined");
      console.log(
        "Bootstrap Modal loaded:",
        typeof bootstrap !== "undefined" &&
          typeof bootstrap.Modal !== "undefined"
      );
    }
  } else {
    console.error(`Modal element with ID "${modalId}" not found`);
  }
}

/**
 * Opens the edit modal for a specific model
 * @param {string} modelName - The model name (e.g., 'Host')
 * @param {string} idField - The ID field name (usually 'id')
 * @param {string|number} idValue - The ID value
 * @param {string} modalId - The modal ID (e.g., 'editHostModal')
 */
function openEditModal(modelName, idField, idValue, modalId) {
  console.log(
    "openEditModal called with:",
    modelName,
    idField,
    idValue,
    modalId
  );

  // Find the modal element
  const modalElement = document.getElementById(modalId);
  if (!modalElement) {
    console.error(`Modal element with ID "${modalId}" not found`);
    return;
  }

  // Make the AJAX request to get the object data
  fetch(
    `/find_obj_with_id/?obj_id=${idValue}&tablename=${modelName}&fieldname=${idField}`
  )
    .then((response) => response.json())
    .then((data) => {
      if (data.status === "success") {
        console.log("Data received:", data.data);

        // Special handling for Event model
        if (modelName === "Event") {
          console.log("Using special handling for Event model");

          // Make sure the modal is visible before populating
          const modal = new bootstrap.Modal(modalElement);
          modal.show();

          // Wait for modal to be fully shown
          modalElement.addEventListener(
            "shown.bs.modal",
            function onModalShown() {
              console.log("Modal shown, populating fields");
              populateEditEventModal(data.data);
              // Remove the event listener to avoid multiple calls
              modalElement.removeEventListener("shown.bs.modal", onModalShown);
            },
            { once: true }
          );
        } else {
          // Set ID field
          const idInput = document.getElementById("id_edit");
          if (idInput) {
            idInput.value = idValue;
          } else {
            console.error("ID input element not found");
          }

          // Set other fields dynamically
          for (const key in data.data) {
            const field = document.getElementById(`${key}_edit`);
            if (field) {
              console.log(`Setting field ${key}_edit to:`, data.data[key]);

              // Handle different field types
              if (field.type === "checkbox") {
                field.checked = Boolean(data.data[key]);
              } else if (field.type === "file") {
                // For file inputs, show preview if available
                if (data.data[key]) {
                  const previewImg = document.getElementById(
                    `preview${key}_edit`
                  );
                  if (previewImg) {
                    previewImg.src = data.data[key];
                    previewImg.style.display = "block";
                  }
                }
              } else if (field.type === "password") {
                // For password fields, leave them empty
                field.value = "";
                // Add a placeholder to indicate that leaving it empty will keep the existing password in italic
                field.placeholder =
                  "Αφήστε κενό για να διατηρήσετε τον υπάρχοντα κωδικό";
              } else if (field.type === "date") {
                    const dateVal = new Date(data.data[key]);
                    if (!isNaN(dateVal)) {
                        field.value = dateVal.toISOString().slice(0, 10); // "YYYY-MM-DD"
                    } else {
                        field.value = "";
                    }
                    } else if (field.type === "time") {
                    const timeVal = data.data[key];
                    if (timeVal && typeof timeVal === "string") {
                        field.value = timeVal.slice(0, 5); // e.g., "10:30:00" → "10:30"
                    } else {
                        field.value = "";
                    }
                    } else {
                    if (data.data[key] !== null && data.data[key] !== undefined) {
                        field.value = data.data[key];
                    } else {
                        field.value = "";
                    }
                }
            }
          }

          // Show the modal
          const modal = new bootstrap.Modal(modalElement);
          modal.show();
        }
      } else {
        console.error("Error fetching data:", data.message);
      }
    })
    .catch((error) => {
      console.error("Error:", error);
    });
}

function deleteFile1(modelName, objectId, fieldName) {
  console.log("Calling deleteFile...");
  console.log("modelName: " + modelName);
  console.log("objectId: " + objectId);
  console.log("fieldName: " + fieldName);

  // Store the parameters for later use
  if (typeof fileToDelete === "undefined") {
    window.fileToDelete = {};
  }

  fileToDelete.modelName = modelName;
  fileToDelete.objectId = objectId;
  fileToDelete.fieldName = fieldName;

  // Get the modal element
  const modalElement = document.getElementById("deleteFileModal");
  if (!modalElement) {
    console.error("Delete file modal not found in the DOM");
    return;
  }

  // Set modal content for file deletion
  const modalLabel = document.getElementById("deleteFileModalLabel");
  if (modalLabel) {
    modalLabel.textContent = "Διαγραφή Αρχείου";
  }

  // Add a custom attribute to identify this as a file deletion
  modalElement.setAttribute("data-delete-type", "file");

  // Show the existing delete modal
  const deleteFileModal = new bootstrap.Modal(modalElement);
  deleteFileModal.show();
}

function uploadFile(modelName, objectId, fieldName, columnType) {
  console.log(
    "Uploading file for:",
    modelName,
    objectId,
    fieldName,
    columnType
  );
  const fileInput = document.createElement("input");
  fileInput.type = "file";
  fileInput.style.display = "none";
  document.body.appendChild(fileInput);

  fileInput.click();

  fileInput.onchange = function () {
    const file = fileInput.files[0];
    if (file) {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("field_name", fieldName);

      // Define the allowed MIME types for each column type
      const allowedMimeTypes = {
        text: [],
        textarea: [],
        number: [],
        datetime: [],
        select: [],
        image: [
          "image/jpeg",
          "image/png",
          "image/gif",
          "image/bmp",
          "image/webp",
          "image/svg+xml",
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ],
        document: [
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/vnd.ms-excel",
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "application/vnd.oasis.opendocument.text",
          "application/vnd.oasis.opendocument.spreadsheet",
          "application/vnd.oasis.opendocument.presentation",
        ],
        // Add more column types and their allowed MIME types as needed
      };

      // Check the file type based on the column type
      if (
        allowedMimeTypes[columnType] &&
        !allowedMimeTypes[columnType].includes(file.type)
      ) {
        alert(`Please select a valid file for the ${fieldName} field.`);
        return;
      }

      fetch(`/calls_center/${modelName}/${objectId}/upload_file/`, {
        method: "POST",
        body: formData,
        headers: {
          "X-CSRFToken": getCookie("csrftoken"),
        },
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            showSuccessToast("Το αρχείο ανέβηκε επιτυχώς!");
            location.reload();
          } else {
            showErrorToast("Error uploading file: " + data.error);
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          showErrorToast("An error occurred while uploading the file.");
        });
    }
    document.body.removeChild(fileInput);
  };
}

// Override the form submission in the delete modal
document.addEventListener("DOMContentLoaded", function () {
  const deleteForm = document.querySelector("#deleteFileModal form");
  if (deleteForm) {
    deleteForm.addEventListener("submit", function (e) {
      // Check if this is a file deletion
      const deleteType = document
        .getElementById("deleteFileModal")
        .getAttribute("data-delete-type");
      if (deleteType === "file") {
        e.preventDefault(); // Prevent the default form submission

        // Hide the modal
        const deleteModal = bootstrap.Modal.getInstance(
          document.getElementById("deleteFileModal")
        );
        deleteModal.hide();

        // Proceed with file deletion
        const formData = new FormData();
        formData.append("field_name", fileToDelete.fieldName);

        // Use the correct URL path without the api prefix
        fetch(
          `/calls_center/${fileToDelete.modelName}/${fileToDelete.objectId}/delete_file/`,
          {
            method: "POST",
            headers: {
              "X-CSRFToken": getCookie("csrftoken"),
            },
            body: formData,
          }
        )
          .then((response) => {
            if (!response.ok) {
              throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
          })
          .then((data) => {
            if (data.success) {
              showSuccessToast("Το αρχείο διαγράφηκε επιτυχώς!");
              location.reload();
            } else {
              console.error("Error response:", data);
              showErrorToast(
                "Error deleting file: " + (data.error || "Unknown error")
              );
            }
          })
          .catch((error) => {
            console.error("Error:", error);
            showErrorToast("An error occurred while deleting the file.");
          });

        // Reset the delete type
        document
          .getElementById("deleteFileModal")
          .removeAttribute("data-delete-type");
      }
      // If not a file deletion, let the form submit normally
    });
  }
});

function togglePasswordVisibility(inputId, btn) {
  const input = document.getElementById(inputId);
  const icon = btn.querySelector("i");

  if (input.type === "password") {
    input.type = "text";
    icon.classList.remove("bi-eye-fill");
    icon.classList.add("bi-eye-slash-fill");
  } else {
    input.type = "password";
    icon.classList.remove("bi-eye-slash-fill");
    icon.classList.add("bi-eye-fill");
  }
}
function updateSelectField(selectElement) {
  const model = selectElement.dataset.model;
  const id = selectElement.dataset.id;
  const field = selectElement.dataset.field;
  const value = selectElement.value;

  console.log(
    `🔄 Changing ${field} of model ${model} (id=${id}) to value: ${value}`
  );

  fetch("/update_field/", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": document.querySelector("[name=csrfmiddlewaretoken]").value,
    },
    body: JSON.stringify({ model, id, field, value }),
  })
    .then((res) => res.text()) // Use .text() instead of .json() temporarily
    .then((text) => {
      console.log("🔍 Raw response:", text);
      try {
        const data = JSON.parse(text);
        console.log("✅ Parsed JSON:", data);
      } catch (err) {
        console.error("❌ Failed to parse JSON:", err);
      }
    })
    .catch((error) => console.error("❌ Network error:", error));
}

// Simple email validation for modals
document.addEventListener("DOMContentLoaded", function () {
  setupEmailValidation();
  setupTinValidation();

  document.querySelectorAll("form").forEach((form) => {
    form.addEventListener("submit", validateFormSubmission);
  });
});

function validateFormSubmission(e) {
  const form = this;

  if (form.dataset.submitting === "true") {
    e.preventDefault();
    return;
  }

  form.dataset.submitting = "true"; // prevent double submit

  const tinInput = form.querySelector('input[name="tin"]');
  const tin = tinInput?.value?.trim();
  const idInput = form.querySelector('input[name="id"]');
  const model = form.querySelector('[name="tablename"]')?.value || "";

  if (idInput && idInput.value) {
    return; // allow submission for updates
  }

  if (model === "CallsCenter" && tin) {
    e.preventDefault();

    fetch(`/check_tin_exists/?tin=${encodeURIComponent(tin)}&model=${model}`)
      .then((response) => response.json())
      .then((data) => {
        if (data.exists) {
          tinInput.classList.add("is-invalid");
          form.dataset.submitting = "false";
        } else {
          tinInput.classList.remove("is-invalid");
          setTimeout(() => form.submit(), 10);
        }
      })
      .catch((err) => {
        console.error("TIN check error:", err);
        setTimeout(() => form.submit(), 10);
      });
  }
}

function setupEmailValidation() {
  document.addEventListener("shown.bs.modal", function (event) {
    const modal = event.target;
    const form = modal.querySelector("form");
    const emailInputs = modal.querySelectorAll(
      'input[type="email"], input[name*="email"], input[id*="email"]'
    );

    emailInputs.forEach((input) => {
      input.type = "email";

      if (!input.parentNode.querySelector(".invalid-feedback")) {
        const feedback = document.createElement("div");
        feedback.className = "invalid-feedback";
        feedback.textContent = "Αυτό το email υπάρχει ήδη.";
        input.parentNode.appendChild(feedback);
      }

      input.addEventListener("input", function () {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = setTimeout(() => {
          const email = this.value.trim();
          if (email.length < 5 || !email.includes("@")) return;

          const model = form?.querySelector('[name="tablename"]')?.value || "";
          const id = form?.querySelector('[name="id"]')?.value || "";

          if (model !== "Prescription") return;

          fetch(
            `/check_email_exists/?email=${encodeURIComponent(
              email
            )}&model=${model}&id=${id}`
          )
            .then((res) => res.json())
            .then((data) => {
              if (data.exists) {
                input.classList.add("is-invalid");
                input.classList.remove("is-valid");
              } else {
                input.classList.remove("is-invalid");
                input.classList.add("is-valid");
              }
            });
        }, 400);
      });
    });

    if (form && emailInputs.length > 0) {
      form.addEventListener("submit", async function (e) {
        if (form.dataset.submitting === "true") return;
        form.dataset.submitting = "true";

        const emailInput = form.querySelector('input[type="email"]');
        const email = emailInput?.value.trim();
        const model = form.querySelector('[name="tablename"]')?.value || "";
        const id = form.querySelector('[name="id"]')?.value || "";

        if (
          model !== "Prescription" ||
          !email ||
          email.length < 5 ||
          !email.includes("@")
        ) {
          form.dataset.submitting = "false";
          return;
        }

        e.preventDefault();

        try {
          const response = await fetch(
            `/check_email_exists/?email=${encodeURIComponent(
              email
            )}&model=${model}&id=${id}`
          );
          const data = await response.json();

          if (data.exists) {
            emailInput.classList.add("is-invalid");
            emailInput.classList.remove("is-valid");
            form.dataset.submitting = "false";
          } else {
            emailInput.classList.remove("is-invalid");
            emailInput.classList.add("is-valid");
            setTimeout(() => form.submit(), 10);
          }
        } catch (error) {
          console.error("Email validation error:", error);
          showErrorToast("Αποτυχία ελέγχου email.");
          setTimeout(() => form.submit(), 10);
        }
      });
    }
  });
}

function setupTinValidation() {
  document
    .querySelectorAll('input[name="tin"], input[id*="tin"]')
    .forEach((input) => {
      input.type = "text";
      input.addEventListener("input", function () {
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {
          const tin = this.value.trim();
          const form = input.closest("form");
          const model = form?.querySelector('[name="tablename"]')?.value || "";

          if (model !== "CallsCenter") return;

          if (tin.length >= 6) {
            fetch(
              `/check_tin_exists/?tin=${encodeURIComponent(tin)}&model=${model}`
            )
              .then((response) => response.json())
              .then((data) => {
                input.classList.toggle("is-invalid", data.exists);
              })
              .catch((err) => {
                console.error("Error checking tin:", err);
              });
          }
        }, 400);
      });

      if (!input.parentNode.querySelector(".invalid-feedback")) {
        const feedback = document.createElement("div");
        feedback.className = "invalid-feedback";
        feedback.textContent = "Αυτό το ΑΦΜ υπάρχει ήδη.";
        input.parentNode.appendChild(feedback);
      }
    });
}

function showSuccessToast(message) {
  if (typeof bootstrap !== "undefined" && bootstrap.Toast) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById("toast-container");
    if (!toastContainer) {
      toastContainer = document.createElement("div");
      toastContainer.id = "toast-container";
      toastContainer.className =
        "toast-container position-fixed bottom-0 end-0 p-3";
      document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastEl = document.createElement("div");
    toastEl.className =
      "toast align-items-center text-white bg-success border-0";
    toastEl.setAttribute("role", "alert");
    toastEl.setAttribute("aria-live", "assertive");
    toastEl.setAttribute("aria-atomic", "true");

    toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

    toastContainer.appendChild(toastEl);

    const toast = new bootstrap.Toast(toastEl);
    toast.show();

    // Remove toast after it's hidden
    toastEl.addEventListener("hidden.bs.toast", function () {
      toastEl.remove();
    });
  } else {
    alert(message);
  }
}

function showErrorToast(message) {
  if (typeof bootstrap !== "undefined" && bootstrap.Toast) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById("toast-container");
    if (!toastContainer) {
      toastContainer = document.createElement("div");
      toastContainer.id = "toast-container";
      toastContainer.className =
        "toast-container position-fixed bottom-0 end-0 p-3";
      document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastEl = document.createElement("div");
    toastEl.className =
      "toast align-items-center text-white bg-danger border-0";
    toastEl.setAttribute("role", "alert");
    toastEl.setAttribute("aria-live", "assertive");
    toastEl.setAttribute("aria-atomic", "true");

    toastEl.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

    toastContainer.appendChild(toastEl);

    const toast = new bootstrap.Toast(toastEl);
    toast.show();

    // Remove toast after it's hidden
    toastEl.addEventListener("hidden.bs.toast", function () {
      toastEl.remove();
    });
  } else {
    alert(message);
  }
}

document.addEventListener('DOMContentLoaded', () => {
    // Add event listeners to filter inputs with debounce
    const filterInputs = document.querySelectorAll('#inline-filter-form input, #inline-filter-form select');
    filterInputs.forEach(input => {
        input.addEventListener('input', debounce(() => {
            document.getElementById('inline-filter-form').submit();
        }, 500));
    });

    // Add change event listeners with proper event handling
    document.querySelectorAll("#inline-filter-form input, #inline-filter-form select").forEach(el => {
        el.addEventListener("change", (event) => {
            // Prevent event propagation to avoid triggering other handlers
            event.stopPropagation();
            
            // Submit the form after a short delay
            setTimeout(() => {
                document.getElementById("inline-filter-form").submit();
            }, 500);
        });
    });

    // Prevent form submission when clicking on action buttons
    document.querySelectorAll('.btn-outline-secondary').forEach(button => {
        button.addEventListener('click', (event) => {
            // Stop the event from bubbling up to the form
            event.stopPropagation();
            // Prevent the default action (which might be form submission)
            event.preventDefault();
            
            // Get the onclick attribute and execute it manually
            const onclickAttr = button.getAttribute('onclick');
            if (onclickAttr) {
                // Execute the onclick function
                eval(onclickAttr);
            }
        });
    });
});

// Debounce function to limit how often a function can be called
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => {
            func.apply(context, args);
        }, wait);
    };
}

function clearFilterInputs() {
    const form = document.getElementById('inline-filter-form');
    if (!form) return;

    const elements = form.querySelectorAll('input, select');
    elements.forEach(el => {
        if (el.type === 'text' || el.type === 'date' || el.tagName.toLowerCase() === 'select') {
            el.value = '';
        }
    });

    form.submit(); // Επαναφορτώνει τη σελίδα με κενά φίλτρα
}

document.addEventListener('DOMContentLoaded', function () {
    flatpickr(".flatpickr", {
        dateFormat: "d/m/Y",
        locale: "gr",
        allowInput: true
    });

     flatpickr(".flatpickr-time", {
        enableTime: true,
        noCalendar: true,
        dateFormat: "H:i",        // Show only hours and minutes
        time_24hr: true,
        allowInput: true,
        locale: "gr"
    });

    flatpickr(".flatpickr-datetime", {
        enableTime: true,
        dateFormat: "d/m/Y H:i",
        time_24hr: true,
        locale: "gr",
        allowInput: true
    });
});
