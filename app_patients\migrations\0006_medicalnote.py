# Generated by Django 5.2.1 on 2025-06-11 09:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_patients', '0005_alter_patient_doctor_email_alter_patient_email_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MedicalNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='medical_notes', to='app_patients.patient')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
