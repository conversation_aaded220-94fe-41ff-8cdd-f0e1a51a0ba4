from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from app_users.models import User
from utilities import utils, messages, status_code, parameters as params
from utilities.token_manager import TokenManager
from utilities.logs_manager import LogsManager
import traceback

default_language = params.default_language
messages = messages.Messages()
status_code_obj = status_code.StatusCode()
token_manager = TokenManager()
logs_manager = LogsManager(params.logs_dir)
en_or_gr = 'el'


def login_user(request):
    try:
        if request.user.is_authenticated:
            next_url = request.GET.get('next') or request.POST.get('next') or '/users/'
            return redirect(next_url)

        if request.method == 'POST':
            email = request.POST.get('email', '').strip()
            password = request.POST.get('password', '').strip()
            
            # Debug information
            print(f"Login attempt for email: {email}")

            if not email or not password:
                return render(request, 'login.html', {
                    'email': email,
                    'message': messages.invalid_credentials.get(en_or_gr),
                })

            # Try to authenticate with email as username
            user = authenticate(request, username=email, password=password)
            
            # Debug information
            if user:
                print(f"Authentication successful for {email}")
            else:
                print(f"Authentication failed for {email}")
                # Check if user exists
                try:
                    user_obj = User.objects.get(email=email)
                    print(f"User exists in database: {user_obj}")
                    print(f"User is active: {user_obj.is_active}")
                    # Check password directly (for debugging only)
                    from django.contrib.auth.hashers import check_password
                    print(f"Raw password check: {check_password(password, user_obj.password)}")
                    print(f"Stored password hash: {user_obj.password}")
                except User.DoesNotExist:
                    print(f"No user found with email: {email}")

            if user:
                login(request, user)
                next_url = request.POST.get('next') or '/users/'
                return redirect(next_url)
            else:
                return render(request, 'login.html', {
                    'email': email,
                    'message': messages.invalid_credentials.get(en_or_gr),
                })

        return render(request, 'login.html', {'email': '', 'message': ''})

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        return render(request, 'login.html', {
            'email': '',
            'success_message': '',
            'error_message': messages.exception(e, en_or_gr),
        })



def register(request):
    try:
        if request.method == 'POST':
            email = request.POST.get('email', '').strip()
            password = request.POST.get('password', '').strip()
            first_name = request.POST.get('first_name', '').strip()
            last_name = request.POST.get('last_name', '').strip()

            if User.objects.filter(email=email).exists():
                return render(request, 'register.html', {
                    'message': messages.user_with_email_already_exists(email, en_or_gr)
                })

            success, message = utils.password_validator(
                password,
                min_len=params.password_minimum_length,
                max_len=params.password_maximum_length,
                numbers=params.password_has_numbers,
                letters=params.password_has_letters,
                lowercase_letters=params.password_has_lowercase_letters,
                uppercase_letters=params.password_has_uppercase_letters,
                has_special_chars=params.password_has_special_chars,
                special_chars=params.password_special_chars,
                language=en_or_gr
            )

            if success:
                user = User(
                    email=email,
                    is_admin=True,
                    is_active=True,
                    administrator_id=User.get_next_administrator_id(),  # This is generating a new administrator_id
                    first_name=first_name,
                    last_name=last_name,
                    role='Διαχειριστής'
                )
                user.set_password(password)
                user.save()
                return render(request, 'register.html', {
                    'message': messages.registration_success.get(en_or_gr),
                    'success': True
                })
            else:
                return render(request, 'register.html', {
                    'email': email,
                    'password': password,
                    'message': message,
                    'success': False
                })

        return render(request, 'register.html', {'message': ''})

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        return render(request, 'register.html', {
            'message': messages.exception(e, en_or_gr)
        })


def logout_user(request):
    logout(request)
    return redirect('login')


def fix_user_passwords(request):
    """
    Fix unhashed passwords for existing users
    Only accessible to superusers
    """
    if not request.user.is_superuser:
        messages.error(request, "Δεν έχετε δικαίωμα πρόσβασης σε αυτή τη σελίδα.")
        return redirect('login')
    
    try:
        # Get all users
        users = User.objects.all()
        fixed_count = 0
        
        for user in users:
            # Check if the password is already hashed
            # Hashed passwords typically start with algorithm identifiers
            if not user.password.startswith(('pbkdf2_sha256$', 'bcrypt$', 'argon2')):
                # Store the plain text password
                plain_password = user.password
                
                # Hash the password properly using the same method as in register
                user.set_password(plain_password)
                user.save()
                
                # Log the fix
                print(f"Fixed password for user: {user.email}")
                fixed_count += 1
        
        messages.success(request, f"Διορθώθηκαν {fixed_count} κωδικοί χρηστών.")
        return redirect('get_users')
    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        messages.error(request, f"Σφάλμα: {str(e)}")
        return redirect('get_users')
