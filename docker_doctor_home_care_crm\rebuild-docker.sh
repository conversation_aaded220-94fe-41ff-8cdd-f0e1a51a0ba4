#!/bin/bash
# This script stops, removes, rebuilds, and restarts only the current project's containers.
# It does NOT remove volumes or affect other unrelated containers.

# Get the directory of this script (even if executed from another location)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Define docker-compose file path dynamically
DOCKER_COMPOSE_PATH="$SCRIPT_DIR/docker-compose.yml"

echo "🔽 Stopping and removing containers for project: $SCRIPT_DIR..."
docker-compose -f "$DOCKER_COMPOSE_PATH" down

echo "🛠️ Removing unused networks related to this project (safe cleanup)..."
docker network prune -f --filter "until=5m" # Ensures active networks are not deleted

echo "🛠️ Rebuilding and starting containers for project: $SCRIPT_DIR..."
docker-compose -f "$DOCKER_COMPOSE_PATH" up --build -d

echo "✅ Containers for project $SCRIPT_DIR have been rebuilt and restarted successfully!"
