# Generated by Django 5.2.1 on 2025-06-11 13:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_patients', '0006_medicalnote'),
    ]

    operations = [
        migrations.CreateModel(
            name='Prescription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('barcode_number', models.Char<PERSON>ield(max_length=256)),
                ('doctor_name', models.Char<PERSON>ield(max_length=256)),
                ('pharmacy_name', models.Char<PERSON>ield(max_length=256)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prescriptions', to='app_patients.patient')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
