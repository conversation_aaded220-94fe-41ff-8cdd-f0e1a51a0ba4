import firebase_admin
from firebase_admin import credentials, messaging
import traceback

import sys

sys.path.append('/opt/airos/local_settings')
sys.path.append('/opt/airos/utilities')
sys.path.append('/opt/airos')

from utilities import logs_manager as logs_manager
from utilities import parameters as params


class Push_notifications_manager:
    logs = logs_manager.Logs(params.logs_dir)

    def __init__(self):
        if not firebase_admin._apps:
            cred = credentials.Certificate(params.firebase_certificate_json)
            firebase_admin.initialize_app(cred)
        # debugging = True
        # if debugging:
        #     tokens = ['daRvm7i1RRK5ple3BVYpbZ:APA91bFdLLhUjwoFeV1SpRIPPdZqEC76St6SzFBbDc-VX2J8Dg6FvETbiqMUfoAktAHsRdR14fbNrEwHiGn6Nu4gZZMTp9B5-US92WxoGWxY_LLpzF_pmsSl8Z3pCBd4D3-SgIeta7x6']
        #     self.send_push_notification_to_tokens(title='test push', body='this is a test push notification via python', token_list=tokens)

    def send_push_notification_to_tokens(self, title, body, image_url, token_list, data=None, android=None,
                                         webpush=None,
                                         apns=None,
                                         fcm_options=None, ):
        """
            message = messaging.MulticastMessage()
            A message that can be sent to multiple tokens via Firebase Cloud Messaging.
            Args:
                   tokens: A list of registration tokens of targeted devices.
                   data: A dictionary of data fields (optional). All keys and values in the dictionary must be
                       strings.
                   notification: An instance of ``messaging.Notification`` (optional).
                   android: An instance of ``messaging.AndroidConfig`` (optional).
                   webpush: An instance of ``messaging.WebpushConfig`` (optional).
                   apns: An instance of ``messaging.ApnsConfig`` (optional).
                   fcm_options: An instance of ``messaging.FCMOptions`` (optional).
        """
        try:
            message = messaging.MulticastMessage(
                tokens=token_list,
                data=data,
                notification=messaging.Notification(title=title, body=body, image=image_url),
                android=messaging.AndroidConfig(collapse_key=None,
                                                priority="high",
                                                ttl=None,
                                                restricted_package_name=None,
                                                data=None,
                                                notification=messaging.AndroidNotification(
                                                    # title=title,
                                                    # body=body,
                                                    priority="high",
                                                    # click_action="FLUTTER_NOTIFICATION_CLICK",
                                                    # default_sound=True,
                                                    # color="#AFEEEE",
                                                    # default_light_settings=True,
                                                    # ticker="Test",
                                                    # icon="favicon.ico",
                                                    # vibrate_timings_millis = [30, 50000, 400, 10000, 400, 100, 400]
                                                ),
                                                fcm_options=None,
                                                direct_boot_ok=None),
                webpush=webpush,
                apns=apns,
                fcm_options=fcm_options
            )
            response = messaging.send_each_for_multicast(message, dry_run=False, app=None)
            # print('Successfully sent message:', response)
            # print(response)
            return True
        except Exception as e:
            traceback.print_exc()
            self.logs.write_error_logs(traceback.format_exc())
            return False

# Push_notifications_manager()
