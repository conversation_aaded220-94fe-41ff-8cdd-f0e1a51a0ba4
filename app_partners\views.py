from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from app_partners.models import Partner
from utilities.services_app import prepare_filters_and_context, prepare_order, pagination
from app_partners.table_configs.tconf_app_partners import FILTERS, COLUMNS, FORM_FIELDS, ADD_MODAL_ID, EDIT_MODAL_ID
import traceback
from utilities import messages as custom_messages  # ✅ your Messages class
from utilities import parameters as params
from utilities.services_app import createOrUpdate
from utilities.logs_manager import LogsManager
from django.contrib import messages
from django.shortcuts import redirect
from django.shortcuts import get_object_or_404
from django.contrib import messages as django_messages
from utilities import logs_manager, utils
from django.db.models import Case, When, Value, IntegerField
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from utilities.services_app import delete_file_by_url

logs_manager = LogsManager(params.logs_dir)
messages_obj = custom_messages.Messages()

@login_required
def get_partners(request):
    try:
        partners = Partner.objects.all()
        partners = partners.filter(administrator_id=request.user.administrator_id)

        query_params = request.GET.dict()
        filters, context_filters, custom_q_filters = prepare_filters_and_context(query_params, "Partner")

        order_by_param = request.GET.get('order_by', '')
        if order_by_param:
            ordering = prepare_order(request, order_by_param)
            field, _ = order_by_param.split('__')
            partners = partners.filter(**filters).annotate(
                null_priority=Case(
                    When(**{f"{field}__isnull": True}, then=Value(1)),
                    default=Value(0),
                    output_field=IntegerField()
                )
            ).order_by('null_priority', ordering)
        else:
            partners = partners.filter(**filters)

        context = {
            'page_obj': pagination(request, partners, 30),
            'order_by': order_by_param,
            **context_filters,
            'filters': FILTERS,
            'columns': COLUMNS,
            'form_fields': FORM_FIELDS,
            'addModalId': ADD_MODAL_ID,
            'editModalId': EDIT_MODAL_ID,
            'add_button_label': 'Νέα Καταχώρηση',
            'add_modal_title': 'Νέα Καταχώρηση Συνεργάτη',
            'edit_modal_title': 'Επεξεργασία Συνεργάτη',
            'modal_description': '',
            'model_name': 'Partner',
            'page_title': 'Συνεργάτες',
            'table_id': 'partners_table',
            'get_action': 'get_partners',
            'modal_delete_obj_description': '',
            'create_update_url': 'createUpdatePartner',  # Use the URL name as a string, not the result of reverse()
            'delete_url': 'delete_partner'  # Use the URL name as a string, not the result of reverse()
        }

        return render(request, 'partners.html', context)

    except Exception as e:
        traceback.print_exc()
        return render(request, 'partners.html', {
            'error_message': f"Σφάλμα: {str(e)}"
        })


def createUpdatePartner(request):
    try:
        if request.method == 'POST':
            print("REQUEST PATH: " + request.path)
            # Get all form data dynamically
            form_data = {key: value.strip() if isinstance(value, str) else value 
                         for key, value in request.POST.items()}
            
            # Check if TIN already exists for this administrator
            tin = form_data.get('tin', '').strip()
            obj_id = form_data.get('id', '').strip()
            
            if tin:
                qs = Partner.objects.filter(
                    tin=tin,
                    administrator_id=request.user.administrator_id
                )
                
                if obj_id:
                    qs = qs.exclude(id=obj_id)
                
                if qs.exists():
                    message = "Αυτό το ΑΦΜ υπάρχει ήδη."
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return JsonResponse({'status': 'error', 'message': message, 'field': 'tin'}, status=400)
                    else:
                        messages.error(request, message)
                        return redirect('get_partners')
            
            # Handle file uploads
            for key, file in request.FILES.items():
                form_data[key] = file
                
            status, message = createOrUpdate(request, form_data)
            if status == 200 or status == 201:
                # Add success message
                messages.success(request, "Ο συνεργάτης αποθηκεύτηκε με επιτυχία!")
                return redirect('get_partners')
            else:
                # Add error message
                context = {
                    'error_message': message
                }
                # Pass it to the template
                return render(request, 'partners.html', context)

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        context = {
            'error_message': messages_obj.exception(e, params.default_language)
        }
        return render(request, 'partners.html', context)


@login_required
def delete_partner(request):
    model_class = Partner
    try:
        if request.method == 'POST':
            obj_id = request.POST.get('obj_id')
            print("delete_partner with id: " + obj_id)

            if not obj_id:
                django_messages.error(request, "Δεν δόθηκε έγκυρο ID για διαγραφή.")
                return redirect('get_partners')

            obj = get_object_or_404(model_class, id=obj_id)
            print(obj)

            # ✅ Διαγραφή logo αρχείου αν υπάρχει
            # if obj.logo and hasattr(obj.logo, 'url'):
            #     from utils.file_utils import delete_file_by_url
            #     delete_file_by_url(obj.logo.url)

            obj.delete()
            django_messages.success(request, "Το αντικείμενο διαγράφηκε με επιτυχία!")
        else:
            django_messages.error(request, "Μη υποστηριζόμενη μέθοδος.")

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        django_messages.error(request, messages_obj.exception(e, params.default_language))

    return redirect('get_partners') 

@login_required
def upload_file(request, model_name, object_id):
    try:
        if model_name == 'Partner':
            model_class = Partner
        else:
            return JsonResponse({'success': False, 'error': f'Unknown model: {model_name}'})

        obj = model_class.objects.get(id=object_id)
        field_name = request.POST.get('field_name')
        file = request.FILES.get('file')

        if not field_name or not file:
            return JsonResponse({'success': False, 'error': 'Missing field_name or file'})

        # Optionally delete old file
        old_file = getattr(obj, field_name)
        if old_file:
            old_file.delete(save=False)

        setattr(obj, field_name, file)
        obj.save()

        return JsonResponse({'success': True})

    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'success': False, 'error': str(e)})
    
@login_required
def delete_file(request, model_name, object_id):
    try:
        field_name = request.POST.get('field_name')
        
        if not field_name:
            return JsonResponse({'success': False, 'error': 'Field name is required'})
        
        # Resolve model
        if model_name == 'Partner':
            model_class = Partner
        else:
            return JsonResponse({'success': False, 'error': f'Unknown model: {model_name}'})
        
        # Get object
        obj = model_class.objects.get(id=object_id)
        
        # Get file field
        file_field = getattr(obj, field_name, None)
        
        # ✅ Use file_field.name (relative path) instead of file_field.url
        if file_field and file_field.name:
            delete_file_by_url(f"/media/{file_field.name}")  # Your util expects full /media/ path
            setattr(obj, field_name, None)
            obj.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'error': 'No file found'})
            
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'success': False, 'error': str(e)})

