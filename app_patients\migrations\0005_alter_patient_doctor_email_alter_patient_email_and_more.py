# Generated by Django 5.2.1 on 2025-06-04 09:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_patients', '0004_alter_patient_amka'),
    ]

    operations = [
        migrations.AlterField(
            model_name='patient',
            name='doctor_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='prescriber_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddConstraint(
            model_name='patient',
            constraint=models.UniqueConstraint(condition=models.Q(models.Q(('email__isnull', True), _negated=True), models.Q(('email', ''), _negated=True)), fields=('email',), name='unique_email_not_null'),
        ),
        migrations.AddConstraint(
            model_name='patient',
            constraint=models.UniqueConstraint(condition=models.Q(models.Q(('doctor_email__isnull', True), _negated=True), models.Q(('doctor_email', ''), _negated=True)), fields=('doctor_email',), name='unique_doctor_email_not_null'),
        ),
        migrations.AddConstraint(
            model_name='patient',
            constraint=models.UniqueConstraint(condition=models.Q(models.Q(('prescriber_email__isnull', True), _negated=True), models.Q(('prescriber_email', ''), _negated=True)), fields=('prescriber_email',), name='unique_prescriber_email_not_null'),
        ),
        migrations.AddConstraint(
            model_name='patient',
            constraint=models.UniqueConstraint(condition=models.Q(models.Q(('amka__isnull', True), _negated=True), models.Q(('amka', ''), _negated=True)), fields=('amka',), name='unique_amka_not_null'),
        ),
    ]
