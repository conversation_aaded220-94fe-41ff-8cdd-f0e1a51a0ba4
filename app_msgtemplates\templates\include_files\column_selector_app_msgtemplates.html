<div class="dropdown">
    <button class="btn btn-primary dropdown-toggle"
            type="button"
            id="columnSelector"
            data-bs-toggle="dropdown"
            aria-expanded="false">
        <i class="fas fa-columns"></i> Στήλες
    </button>
    <div class="dropdown-menu p-4"
         aria-labelledby="columnSelector"
         style="width: 250px">
        {% for column in columns %}
            {% if not column.required %}
                <div class="form-check">
                    <input class="form-check-input column-toggle"
                           type="checkbox"
                           value="{{ column.id }}"
                           id="column_{{ column.id }}"
                           {% if column.default_visible %}checked{% endif %}
                           data-table-id="{{ table_id }}">
                    <label class="form-check-label" for="column_{{ column.id }}">{{ column.label }}</label>
                </div>
            {% endif %}
        {% endfor %}
    </div>
</div>
