ADD_MODAL_ID = 'addModalId'
EDIT_MODAL_ID = 'editModalId'

COLUMNS = [
    {
        'id': 'id',
        'label': '#',
        'field': 'id',
        'type': 'counter',
        'required': True,
        'default_visible': True
    },
    {
        'id': 'first_name',
        'label': 'ΟΝΟΜΑ',
        'field': 'first_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'last_name',
        'label': 'ΕΠΩΝΥΜΟ',
        'field': 'last_name',
        'type': 'text',
        'required': False,
        'default_visible': True
    },
    {
        'id': 'email',
        'label': 'EMAIL',
        'field': 'email',
        'type': 'text',
        'required': True,
        'default_visible': True
    },
    # {
    #     'id': 'password',
    #     'label': 'PASSWORD',
    #     'field': 'password',
    #     'type': 'password',
    #     'required': True,
    #     'default_visible': False
    # },
    {
        'id': 'is_active',
        'label': 'ΚΑΤΑΣΤΑΣΗ',
        'field': 'is_active',
        'type': 'status',
        'required': False,
        'default_visible': True,
        'options': [
             {'value': True, 'label': 'Ενεργός'},
             {'value': False, 'label': 'Μη Ενεργός'}
        ]
    },
    {
        'id': 'role',
        'label': 'ΔΥΝΑΤΟΤΗΤΕΣ ΧΡΗΣΤΗ',
        'field': 'role',
        'type': 'select',
        'required': False,
        'default_visible': True,
        'options': [
            {'value': 'Διαχειριστής', 'label': 'Διαχειριστής'},
            {'value': 'Νοσηλευτής', 'label': 'Νοσηλευτής'},
            # {'value': 'Πελάτης', 'label': 'Πελάτης'}
        ]
    },
    {
        'id': 'actions',
        'label': 'ΕΝΕΡΓΕΙΕΣ',
        'type': 'actions',
        'required': True,
        'default_visible': True
    }
]

FILTERS = [
    {
        'id': 'order_by',
        'name': 'order_by',
        'label': 'Ταξινόμηση',
        'type': 'select',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
        },
        'options': [
            {'value': 'first_name__asc', 'label': 'Όνομα Αύξουσα ↑'},
            {'value': 'first_name__desc', 'label': 'Όνομα Φθίνουσα ↓'}
        ]
    },
    {
        'id': 'last_name_filter',
        'name': 'last_name__contains',
        'label': 'Επώνυμο',
        'type': 'text',
        'width': '180px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-control border-secondary rounded-pill'
        }
    },
    {
        'id': 'is_active_filter',
        'name': 'is_active',
        'label': 'Κατάσταση',
        'type': 'select',
        'width': '150px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
        },
        'options': [
            {'value': 'True', 'label': 'Ενεργός'},
            {'value': 'False', 'label': 'Μη Ενεργός'}
        ]
    },

    {
        'id': 'role_filter',
        'name': 'role',
        'label': 'Ρόλος',
        'type': 'select',
        'width': '150px',
        'style': {
            'container': 'mb-3 me-2',
            'label': 'text-secondary fw-bold fs-6',
            'input': 'form-select border-secondary'
        },
        'options': [
            {'value': 'Διαχειριστής', 'label': 'Διαχειριστής'},
            {'value': 'Νοσηλευτής', 'label': 'Νοσηλευτής'},
            # {'value': 'Πελάτης', 'label': 'Πελάτης'}
        ]
    }
]

FORM_FIELDS = [
    {
        'id': 'first_name',
        'label': 'Όνομα',
        'type': 'text',
        'required': True,
        'width': '12',
        'placeholder': 'Εισάγετε όνομα'
    },
    {
        'id': 'last_name',
        'label': 'Επώνυμο',
        'type': 'text',
        'required': True,
        'width': '12',
        'placeholder': 'Εισάγετε επώνυμο'
    },
    {
        'id': 'email',
        'label': 'Email',
        'type': 'email',
        'required': True,
        'width': '12',
        'placeholder': 'Εισάγετε email'
    },
    {
        'id': 'password',
        'label': 'Κωδικός',
        'type': 'password',
        'required': True,
        'width': '12',
        'placeholder': 'Εισάγετε κωδικό'
    },
    {
        'id': 'role',
        'label': 'Ρόλος',
        'type': 'select',
        'required': True,
        'width': '12',
        'options': [
            {'value': 'Διαχειριστής', 'label': 'Διαχειριστής'},
            {'value': 'Νοσηλευτής', 'label': 'Νοσηλευτής'},
            # {'value': 'Πελάτης', 'label': 'Πελάτης'}
        ]
    }
]


