# import datetime
from datetime import datetime
import traceback
import os
# from utilities import parameters as params


# from utilities import utils


class LogsManager:

    logs_dir = None

    def __init__(self, logs_dir):
        self.logs_dir = logs_dir
        _ = None



    def write_error_logs(self, text):
        f = None
        try:
            error_logs_file_path, _ = self.get_error_and_functional_logs_file_paths(logs_dir=self.logs_dir)
            now = datetime.now()
            date_time_str = now.strftime("%Y-%m-%d %H:%M:%S")
            write_str = f'[{date_time_str}] {text.strip()}\n\n'
            f = self.open_or_create_a_file(error_logs_file_path, opening_mode='a',
                                           add_blank_space_in_first_line=True)
            f.write(write_str)
            f.close()
        except Exception as e:
            traceback.print_exc()
            if f:
                try:
                    f.close()
                except Exception as e:
                    traceback.print_exc()

    def write_functional_logs(self, text):
        f = None
        try:
            _, functional_logs_file_path = self.get_error_and_functional_logs_file_paths(self.logs_dir)
            now = datetime.datetime.now()
            date_time_str = now.strftime("%Y-%m-%d %H:%M")
            write_str = f'[{date_time_str}] {text.strip()}\n\n'
            f = self.open_or_create_a_file(functional_logs_file_path, opening_mode='a',
                                           add_blank_space_in_first_line=True)
            f.write(write_str)
            f.close()
        except Exception as e:
            traceback.print_exc()
            if f:
                try:
                    f.close()
                except Exception as e:
                    traceback.print_exc()


    def get_error_and_functional_logs_file_paths(self, logs_dir):
        current_dir = os.getcwd()
        # logs_dir = f'{current_dir}/logs'
        self.create_dir_if_not_exists(logs_dir)
        currentDay = datetime.now().day
        currentMonth = datetime.now().month
        currentYear = datetime.now().year
        day_of_creating_log_file = '01'
        if currentDay < 16:
            day_of_creating_log_file = '01'
        elif currentDay > 15:
            day_of_creating_log_file = '16'
        functional_logs_filename = '{}_{}_{}_functional_logs.txt'.format(currentYear, currentMonth,
                                                                         day_of_creating_log_file)
        error_logs_filename = '{}_{}_error_logs.txt'.format(currentYear, currentMonth)
        functional_logs_file_path = f'{logs_dir}/{functional_logs_filename}'
        error_logs_file_path = f'{logs_dir}/{error_logs_filename}'
        return error_logs_file_path, functional_logs_file_path

    def open_or_create_a_file(self, filepath, opening_mode, add_blank_space_in_first_line=False):
        if self.check_file_existence(filepath):
            file = open(filepath, opening_mode, encoding='utf8')
            return file
        else:
            file = open(filepath, 'w+', encoding='utf8')
            if add_blank_space_in_first_line:
                file.write(' ')
            file.close()
            file = open(filepath, opening_mode, encoding='utf8')
            return file

    def check_file_existence(self, filepath):
        return os.path.isfile(filepath)

    def create_dir_if_not_exists(self, dir_path):
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)
