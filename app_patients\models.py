from django.db import models

class Patient(models.Model):
    administrator_id = models.Integer<PERSON>ield(null=False, blank=False)

    amka = models.CharField(max_length=11, null=True, blank=True)  # now nullable
    first_name = models.Char<PERSON><PERSON>(max_length=50, blank=False)
    last_name = models.Char<PERSON>ield(max_length=50, blank=False)
    gender = models.Char<PERSON><PERSON>(max_length=20, blank=False)
    father_name = models.Char<PERSON>ield(max_length=50, blank=False)
    date_of_birth = models.DateField(null=True, blank=True)
    recommendation = models.Char<PERSON>ield(blank=False)
    contract = models.Char<PERSON><PERSON>(max_length=50, blank=False)
    dressing = models.Char<PERSON>ield(max_length=50, blank=False)
    address = models.TextField(blank=False)
    area = models.CharField(max_length=256, blank=False)
    floor = models.Char<PERSON>ield(max_length=256, blank=False)
    bell = models.Char<PERSON><PERSON>(max_length=256, blank=False)
    postal_code = models.Char<PERSON>ield(max_length=20, blank=False)
    city = models.Char<PERSON><PERSON>(max_length=256, blank=False)
    medical_condition = models.Char<PERSON><PERSON>(max_length=256, blank=False)
    phone = models.CharField(max_length=256, blank=False)

    # Nullable + custom uniqueness constraint
    email = models.EmailField(null=True, blank=True)
    doctor_email = models.EmailField(null=True, blank=True)
    prescriber_email = models.EmailField(null=True, blank=True)

    icd10_code = models.CharField(max_length=256, blank=False)
    doctor_name = models.CharField(max_length=256, blank=False)
    doctor_phone = models.CharField(max_length=256, blank=False)
    eprescription = models.CharField(max_length=256, blank=False)
    kepa_decision_number = models.CharField(max_length=256, blank=False)
    kepa_decision_date = models.DateField(null=True, blank=True)
    insurance_provider = models.CharField(max_length=256, blank=False)
    pharmacy_name = models.CharField(max_length=256, blank=False)
    prescriber_name = models.CharField(max_length=256, blank=False)
    prescriber_phone = models.CharField(max_length=256, blank=False)
    patient_history = models.TextField(blank=False)
    financial_offers = models.TextField(blank=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name}"

    class Meta:
        ordering = ['created_at']
        constraints = [
            models.UniqueConstraint(
                fields=['email'],
                name='unique_email_not_null',
                condition=~models.Q(email__isnull=True) & ~models.Q(email='')
            ),
            models.UniqueConstraint(
                fields=['doctor_email'],
                name='unique_doctor_email_not_null',
                condition=~models.Q(doctor_email__isnull=True) & ~models.Q(doctor_email='')
            ),
            models.UniqueConstraint(
                fields=['prescriber_email'],
                name='unique_prescriber_email_not_null',
                condition=~models.Q(prescriber_email__isnull=True) & ~models.Q(prescriber_email='')
            ),
            models.UniqueConstraint(
                fields=['amka', 'administrator_id'],
                name='unique_amka_per_administrator',
                condition=~models.Q(amka__isnull=True) & ~models.Q(amka='')
            ),
        ]

class MedicalNote(models.Model):
    patient = models.ForeignKey('Patient', on_delete=models.CASCADE, related_name='medical_notes')
    note = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']  # Default order: latest first

    def __str__(self):
        return f"Note for {self.patient} on {self.created_at.strftime('%d/%m/%Y')}"

# class Prescription(models.Model):
#     patient = models.ForeignKey('Patient', on_delete=models.CASCADE, related_name='prescriptions')
#     date = models.DateField(auto_now_add=True)  
#     barcode_number = models.CharField(max_length=256)
#     prescriber_name = models.CharField(max_length=256)
#     pharmacy_name = models.CharField(max_length=256)
#     created_at = models.DateTimeField(auto_now_add=True)

#     class Meta:
#         ordering = ['-created_at']  # Default order: latest first
        
#     def __str__(self):
#         return f"Prescription for {self.patient} on {self.date}"
