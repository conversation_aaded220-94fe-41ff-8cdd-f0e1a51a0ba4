# Generated by Django 5.2.1 on 2025-05-29 12:30

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Partner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tin', models.Char<PERSON>ield(blank=True, default='', max_length=20)),
                ('name', models.<PERSON>r<PERSON><PERSON>(blank=True, default='', max_length=256)),
                ('surname', models.<PERSON>r<PERSON><PERSON>(blank=True, default='', max_length=256)),
                ('medical_specialty', models.CharField(blank=True, default='', max_length=256)),
                ('address', models.TextField(blank=True, default='')),
                ('phone', models.Char<PERSON>ield(blank=True, default='', max_length=256)),
                ('email', models.EmailField(blank=True, default='', max_length=254)),
                ('partnership_agreement', models.FileField(blank=True, null=True, upload_to='partnership_agreements/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
