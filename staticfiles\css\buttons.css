.add-btn {
    background-color: #65cfc4;
    color: white;
    border-radius: 999px;
    padding: 0 14px;
    font-weight: bold;
    font-size: 1rem;
    border: none;
    display: inline-flex;
    align-items: center;
    /* gap: 8px; */
}

.add-btn i {
    font-size: 1rem;
}

.add-btn:hover {
    background-color: #56bdb3;
}

.delete-btn {
    background-color: #65cfc4;
    color: white;
    border-radius: 999px;
    padding: 0.5rem 1rem;
    font-weight: bold;
    font-size: 1rem;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center; /* ✅ Add this */
    text-align: center;       /* ✅ Add this */
    transition: background-color 0.3s ease;
}


.delete-btn:hover {
    background-color: #56bdb3;
}

.btn-dark, 
.btn-outline-dark {
    background-color: #212529 !important;
    border-color: #212529 !important;
    color: white !important;
}

.btn-dark:hover, 
.btn-dark:focus,
.btn-outline-dark:hover,
.btn-outline-dark:focus {
    background-color: #7FEFDF!important;
    border-color: #000000 !important;
    color: #424242 !important;
}

.btn-outline-dark {
    background-color: rgb(246, 246, 246) !important;
    color: #000000 !important;
}

/* Rounded button with hover effect */
.custom-icon-btn {
    border-radius: 999px;
    padding: 6px 10px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    border: 1px solid #ccc;
    transition: background-color 0.2s ease;
}

.custom-icon-btn:hover {
    background-color: #f2f2f2;
}

/* Mint icon color for edit */
.custom-icon-mint {
    color: #56bdb3;
    font-size: 1rem;
}

/* Red icon color for delete */
.custom-icon-red {
    color: #dc3545;
    font-size: 1rem;
}

.custom-icon-btn:hover .custom-icon-mint {
    color: #3daaa1;
}

.custom-icon-btn:hover .custom-icon-red {
    color: #c82333;
}

.custom-columns-btn {
  color: #65cfc4;
  font-weight: 800;
  font-size: 1.1rem;
  border: none;
  border-radius: 999px;
  display: inline-flex;
  align-items: center;
  transition: background-color 0.3s ease;
}

.custom-columns-btn i {
  font-size: 1.4rem;
}

.custom-columns-btn:hover {
  color: #56bdb3;
}
