{% load custom_filters %}
<div class="modal fade"
     id="{{ modal_id }}"
     tabindex="-1"
     aria-labelledby="{{ modal_id }}Label"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content rounded-4 border-0 shadow-sm">
            <div class="modal-header border-1 border-bottom py-4">
                <h5 class="modal-title" id="{{ modal_id }}Label">
                    <strong>{{ modal_title }}</strong>
                </h5>
                <button type="button"
                        class="btn-close"
                        data-bs-dismiss="modal"
                        aria-label="Close"></button>
            </div>
            <div class="modal-body px-6">
                <form id="{{ form_id }}"
                      method="POST"
                      action="{% url 'createUpdateCallsCenter' %}"
                      enctype="multipart/form-data"
                      data-ajax-submit="true">
                    {% csrf_token %}
                    <input type="hidden" id="id_{{ mode }}" name="id" value="">
                    <input type="hidden" name="tablename" value="{{ model_name }}">
                    <div class="row">
                        {% for field in form_fields %}
                            <div class="col-md-{{ field.width|default:'12' }} mb-3">
                                <label for="{{ field.id }}_{{ mode }}" class="form-label">{{ field.label }}</label>
                                {% if field.type == 'email' %}
                                    <div class="form-group">
                                        <input type="email"
                                               id="{{ field.id }}_{{ mode }}"
                                               name="{{ field.name|default:field.id }}"
                                               class="form-control rounded-pill"
                                               {% if field.required %}required{% endif %}
                                               placeholder="{{ field.placeholder }}">
                                        <div class="invalid-feedback">Αυτό το email υπάρχει ήδη.</div>
                                    </div>
                                {% elif field.type == 'text' %}
                                    <div class="form-group">
                                        <input type="text"
                                               id="{{ field.id }}_{{ mode }}"
                                               name="{{ field.name|default:field.id }}"
                                               class="form-control rounded-pill"
                                               {% if field.required %}required{% endif %}
                                               placeholder="{{ field.placeholder }}">
                                    </div>
                                {% elif field.type == 'password' %}
                                    <div class="position-relative">
                                        <input type="password"
                                               id="{{ field.id }}_{{ mode }}"
                                               name="{{ field.name|default:field.id }}"
                                               class="form-control rounded-pill pe-5"
                                               {% if mode == 'add' %}{% if field.required %}required{% endif %}
                                               {% endif %}
                                               placeholder="{% if mode == 'edit' %}Αφήστε κενό για να διατηρήσετε τον υπάρχοντα κωδικό{% else %}{{ field.placeholder }}{% endif %}">
                                        <button type="button"
                                                class="btn position-absolute end-0 top-50 translate-middle-y me-3 p-0 border-0 bg-transparent text-muted"
                                                style="z-index: 10"
                                                onclick="togglePasswordVisibility('{{ field.id }}_{{ mode }}', this)">
                                            <i class="bi bi-eye-fill"></i>
                                        </button>
                                    </div>
                                {% elif field.type == 'select' %}
                                    <select id="{{ field.id }}_{{ mode }}"
                                            name="{{ field.name|default:field.id }}"
                                            class="form-select rounded-pill"
                                            {% if field.required %}required{% endif %}>
                                        <option value="">Επιλέξτε...</option>
                                        {% for option in field.options %}<option value="{{ option.value }}">{{ option.label }}</option>{% endfor %}
                                    </select>
                                {% elif field.type == 'date' %}
                                    <input type="text"
                                           id="{{ field.id }}_{{ mode }}"
                                           name="{{ field.name|default:field.id }}"
                                           class="form-control rounded-pill flatpickr"
                                           placeholder="{{ field.placeholder }}"
                                           autoComplete="off">
                                {% elif field.type == 'time' %}
                                    <div class="form-group">
                                        <input type="text"
                                               id="{{ field.id }}_{{ mode }}"
                                               name="{{ field.name|default:field.id }}"
                                               class="form-control rounded-pill flatpickr-time h-100"
                                               {% if field.required %}required{% endif %}
                                               placeholder="{{ field.placeholder }}"
                                               autoComplete="off">
                                    </div>
                                {% elif field.type == 'textarea' %}
                                    <div class="form-group">
                                        <textarea id="{{ field.id }}_{{ mode }}"
                                                  name="{{ field.name|default:field.id }}"
                                                  class="form-control rounded-3"
                                                  rows="4"
                                                  style="resize: vertical"
                                                  {% if field.required %}required{% endif %}>{{ field.value }}</textarea>
                                    </div>
                                {% elif field.type == 'file' %}
                                    {% if file_value %}
                                        <div class="mb-2">
                                            <a href="{{ file_value.url }}"
                                               target="_blank"
                                               class="btn btn-sm btn-outline-info">Προβολή υπάρχοντος αρχείου</a>
                                        </div>
                                    {% endif %}
                                    <div class="form-group">
                                        <input type="file"
                                               id="{{ field.id }}_{{ mode }}"
                                               name="{{ field.name|default:field.id }}"
                                               class="form-control"
                                               {% if field.required %}required{% endif %}>
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                    <div class="d-flex justify-content-end mt-3">
                        <button type="reset"
                                class="btn btn-outline-dark me-2 rounded-pill px-4 py-2"
                                data-bs-dismiss="modal">Ακύρωση</button>
                        <button type="submit" class="btn btn-dark rounded-pill px-4 py-2">Αποθήκευση</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
