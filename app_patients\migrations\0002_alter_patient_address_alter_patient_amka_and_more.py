# Generated by Django 5.2.1 on 2025-06-04 09:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app_patients', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='patient',
            name='address',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='patient',
            name='amka',
            field=models.Char<PERSON>ield(max_length=20, unique=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='area',
            field=models.Char<PERSON>ield(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='bell',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='city',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='contract',
            field=models.Char<PERSON>ield(max_length=50),
        ),
        migrations.Alter<PERSON><PERSON>(
            model_name='patient',
            name='doctor_email',
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='doctor_name',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='doctor_phone',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='dressing',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='patient',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='eprescription',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='father_name',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='patient',
            name='financial_offers',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='patient',
            name='first_name',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='patient',
            name='floor',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='gender',
            field=models.CharField(max_length=20),
        ),
        migrations.AlterField(
            model_name='patient',
            name='icd10_code',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='insurance_provider',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='kepa_decision_number',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='last_name',
            field=models.CharField(max_length=50),
        ),
        migrations.AlterField(
            model_name='patient',
            name='medical_condition',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='patient_history',
            field=models.TextField(),
        ),
        migrations.AlterField(
            model_name='patient',
            name='phone',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='postal_code',
            field=models.CharField(max_length=20),
        ),
        migrations.AlterField(
            model_name='patient',
            name='prescriber_email',
            field=models.EmailField(blank=True, max_length=254, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name='patient',
            name='prescriber_name',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='prescriber_phone',
            field=models.CharField(max_length=256),
        ),
        migrations.AlterField(
            model_name='patient',
            name='recommendation',
            field=models.CharField(),
        ),
        migrations.AlterField(
            model_name='patient',
            name='serving_pharmacy',
            field=models.CharField(max_length=256),
        ),
    ]
