/* th input, th select {
  max-width: 100%;
  font-size: 0.8125rem;
  padding: 4px 10px;
}

.custom-tabs {
  display: flex;
  flex-wrap: nowrap;
  border-bottom: 2px solid #e0e0e0;
  font-size: 1.1rem;
  font-weight: 600;
  gap: 2rem;
  overflow-x: auto;
  padding-bottom: 8px;
  margin-top: 1rem;
}

.tab-item {
  cursor: pointer;
  color: #0f2c2e;
  position: relative;
  padding-bottom: 6px;
  white-space: nowrap;
  transition: color 0.2s ease;
}

.tab-item:hover {
  color: #2da29b;
}

.tab-item.active {
  color: #2da29b;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #2da29b;
  border-radius: 2px;
}

 */
 .custom-tabs {
  background-color: lightblue !important;
}

