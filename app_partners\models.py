from django.db import models

class Partner(models.Model):
    administrator_id = models.IntegerField(null=False, blank=False)
    tin = models.CharField(max_length=9, default='', blank=False) 
    first_name = models.CharField(max_length=256, default='', blank=False, null=False)
    last_name = models.CharField(max_length=256, default='', blank=False, null=False)
    medical_specialty = models.CharField(max_length=256, default='', blank=False, null=False)
    address = models.TextField(default='', blank=False, null=False)
    phone = models.CharField(max_length=256, default='', blank=False)
    email = models.EmailField(default='', blank=False, null=False)
    partnership_agreement = models.FileField(
        upload_to='partnership_agreements/',
        null=True,
        blank=True
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.tin})"

    class Meta:
        ordering = ['created_at']
        constraints = [
            # Add constraint for unique TIN per administrator_id
            models.UniqueConstraint(
                fields=['tin', 'administrator_id'],
                name='unique_tin_per_administrator',
                condition=~models.Q(tin='')
            ),
        ]
        
