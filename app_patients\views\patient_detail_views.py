from django.shortcuts import render, get_object_or_404
from app_patients.models import Patient
from django.shortcuts import redirect
from app_patients.table_configs.tconf_app_patients import FORM_FIELDS
from app_patients.forms import MedicalNoteForm
from app_patients.forms import PrescriptionForm
from app_patients.models import MedicalNote
from django.views.decorators.http import require_POST
from django.contrib import messages
from utilities.utils import generate_barcode_base64
from app_prescriptions.models import Prescription


def patient_tab_content(request, patient_id, tab):
    patient = get_object_or_404(Patient, id=patient_id)
    template_name = f"tabs/{tab}.html"
    context = {
        "patient": patient,
        "tab": tab,
        "tab_template": template_name,
        "form_fields": FORM_FIELDS,
    }

    # TAB: Medical Notes
    if tab == "medical_notes":
        if request.method == 'POST':
            form = MedicalNoteForm(request.POST)
            if form.is_valid():
                note = form.save(commit=False)
                note.patient = patient
                note.save()
                return redirect('patient_tab_content', patient_id=patient.id, tab='medical_notes')
        else:
            form = MedicalNoteForm()

        context["form"] = form
        context["notes"] = patient.medical_notes.order_by("-created_at")

    # TAB: Prescriptions
    elif tab == "prescriptions":
        if request.method == "POST":
            form = PrescriptionForm(request.POST)
            if form.is_valid():
                prescription = form.save(commit=False)
                prescription.patient = patient
                # Set the administrator_id from the request user
                prescription.administrator_id = request.user.administrator_id
                prescription.save()
                return redirect('patient_tab_content', patient_id=patient.id, tab='prescriptions')
        else:
            form = PrescriptionForm(initial={'patient': patient})

        # Inject base64 barcode here if needed
        prescriptions = patient.prescriptions.order_by("-date")
        for p in prescriptions:
            p.patient_name = str(patient)
            p.barcode_base64 = generate_barcode_base64(p.barcode_number)

        context["prescription_form"] = form
        context["prescriptions"] = prescriptions

    return render(request, "patient_detail.html", context)



# def medical_notes_view(request, patient_id):
#     patient = get_object_or_404(Patient, id=patient_id)

#     if request.method == 'POST':
#         form = MedicalNoteForm(request.POST)
#         if form.is_valid():
#             note = form.save(commit=False)
#             note.patient = patient
#             note.save()
#             return redirect('medical_notes', patient_id=patient.id)
#         else:
#             print("❌ Form errors:", form.errors)

#     else:
#         form = MedicalNoteForm()

#     notes = patient.medical_notes.order_by('-created_at')  # Ταξινομημένα

#     return render(request, 'tabs/medical_notes.html', {
#         'patient': patient,
#         'notes': notes,
#         'form': form,
#     })

@require_POST
def delete_medical_note(request, patient_id, note_id):
    note = get_object_or_404(MedicalNote, id=note_id, patient_id=patient_id)
    note.delete()
    messages.success(request, "Η ιατρική οδηγία διαγράφηκε.")
    return redirect('patient_tab_content', patient_id=patient_id, tab='medical_notes')

@require_POST
def update_medical_note(request, patient_id, note_id):
    patient = get_object_or_404(Patient, id=patient_id)
    note = get_object_or_404(MedicalNote, id=note_id, patient=patient)

    form = MedicalNoteForm(request.POST, instance=note)
    if form.is_valid():
        form.save()
        messages.success(request, "Η ιατρική οδηγία ενημερώθηκε.")
    else:
        messages.error(request, "Σφάλμα κατά την ενημέρωση.")

    return redirect('patient_tab_content', patient_id=patient_id, tab='medical_notes')
