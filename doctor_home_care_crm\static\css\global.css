/* Font import and global reset */
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap');

/* Reset for ALL elements with maximum specificity */
html body,
html body *,
html body *::before,
html body *::after {
    font-family: 'Manrope', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif !important;
}

/* Target specific elements that might be problematic */
button, input, select, textarea, a, p, h1, h2, h3, h4, h5, h6, span, div, li, ul, ol, table, th, td {
    font-family: 'Manrope', sans-serif !important;
}

/* Rest of your global.css content */
/*********** select (START) *****************/


/*********** select (end) *****************/


/*********** colors (START) *****************/
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700&display=swap');

/* Apply Manrope to ALL elements */
html, body, input, textarea, button, select, a, label, span, p, h1, h2, h3, h4, h5, h6, table, th, td {
    font-family: 'Manrope', sans-serif !important;
}

/* I want a variable for a color in order to use it globally */
:root {
    --mint-color: #DFFFFC;
    --gray-color: #727272;
    --danger-color: #A52D2E;
    --black-color: black;
}


.table-hover tbody tr:hover td, .table-hover tbody tr:hover th {
    background-color: var(--mint-color);
    cursor: pointer;
}

.logo-header-img {
    max-height: 55px;
    height: auto;
}


.color-primary {
    color: #4692FF;
}

.bg-color-primary {
    background: #4692FF;
}

.color-primary-light {
    color: #DEECFF;
}

.bg-color-primary-light {
    background: #DEECFF;
}

.color-secondary {
    color: #5646FF;
}

.bg-color-secondary {
    background: #5646FF;
}

.color-analogy {
    color: #46EEFF;
}

.bg-color-analogy {
    background: #46EEFF;
}

.color-gray {
    color: var(--gray-color) !important;
}

.color-danger {
    color: #A52D2E !important;
}

.color-black {
    color: black !important;
}

/*********** colors (END) *****************/

.error-message {
    padding: 5px;
    color: red;
    background-color: #f8d7da;
    margin-bottom: 10px;
}

input:-webkit-autofill {
    background-color: white !important; /* Or any desired color */
    color: black !important; /* Ensure the text is visible */
    transition: background-color 5000s ease-in-out 0s; /* Prevent autofill color flash */
}

.main-bg-color {
    background-color: #eaeaea;
}

/* .table-shadow {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 1.5);
    border-radius: 50px; 
    overflow: hidden; 
} */

.btn-gray {
    color: #adb7ba !important; /* Light text */
    background-color: #FFFFFF !important; /* White background */
    border-color: #adb7ba !important;/* Light border */
}

/* .:focus {
  border-color: #ccc !important;   *//* Set a custom border color (e.g., light gray) *//*
  box-shadow: none !important;      *//* Remove the focus box-shadow *//*
} */

.required:after {
    content:"*";
    color: #4692FF;
}

/*********  Modal css  ***************************/

.btn-danger-outline {
    background-color: white !important;
    border-color: white !important;
    color: #4692FF !important;
}

.btn-danger-outline:hover {
    background-color: #4692FF !important;
    color: white !important;
}

.btn-primary {
    background-color: #4692FF !important;
    border-color: #4692FF !important;
    color: white !important;
}

.btn-primary:hover {
    background-color: #4692FF !important;
    color: white !important;;
}

.btn-primary-lighter {
    background-color: #deecff !important;
    border-color: #deecff !important;
    color: white !important;
}

.btn-primary-lighter:hover {
    background-color: #deecff !important;
    color: white !important;;
}

.btn-secondary {
    background-color: #5646FF !important;
    border-color: #5646FF !important;
    color: white !important;
}

.btn-secondary:hover {
    background-color: #5646FF !important;
    color: white !important;
}

.btn-analogy {
    background-color: #46EEFF !important;
    border-color: #46EEFF !important;
    color: white !important;
}

.btn-analogy:hover {
    background-color: #46EEFF !important;
    color: white !important;
}


/**************  Filters *******************/

.filter-form-select {
    color: #808080;
    padding: 0.5rem 1rem;  /* Add padding for better spacing */
    font-size: 1rem;       /* Set font size */
    border-radius: 0.375rem; /* Rounded corners */
    border: 1px solid #ccc;  /* Border color */
    background-color: #fff ;  /* White background */
    transition: border-color 0.3s ease; /* Smooth transition on focus */
}

.filter-form-select:focus {
    border-color: #ECECEC;  /* Change border color on focus */
    outline: none;  /* Remove default outline */
    box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.25); /* Blue shadow */
}

/****************** CSS for tables ***********************/
.table {
    /* border: 2px solid #ECECEC;  */
    border-radius: 20px;  /* Rounded corners for the table */
    border-collapse: separate; 
    padding: 10px;
    /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
}

.table-wrapper {
    border-radius: 2rem; /* Adjust as needed */
    overflow: hidden;
    border: 1px solid #dee2e6; /* Optional for border around the table */
    padding: 4px;
}


.table th {
    vertical-align: top;
    text-align: center;
    font-size: 12px;
}

.table td {
    vertical-align: middle;
    text-align: center;
    font-size: 14px !important;
}

/***************** close button in modals *************/
.custom-close-btn {
  background-color: #4692FF; /* Danger background color */
  color: white; /* White text for the "X" */
  border: none;
  border-radius: 50%; /* Circular button */
  width: 30px;
  height: 30px;
  font-size: 16px; /* Adjust font size for the "X" */
  line-height: 30px; /* Center align text vertically */
  text-align: center; /* Center align text horizontally */
  cursor: pointer;
  padding: 0;
}

.gray {
    color: gray;
    opacity: 0.5;
}


/* Apply color to the SELECT element itself */
.bg-mint {
    background-color: #6AC7BA !important;
}
.bg-gray {
    background-color: var(--gray-color) !important;
}

.bg-gray-status {
    background-color: #ADADAD !important;
}

.text-bold {
    font-weight: bold;
}

/* Ensure dropdown options are readable (not gray bg) */
select.status-select option {
    background-color: white;
    color: black;
}

/* I want select on hover */

.table tbody td {
    color: var(--gray-color);
    font-weight: 700;
}


.red-bg-and-text-for-fullname {
    display: inline-block;
    background-color: #F0DBDB !important;
    color: #B04748 !important;
    border-radius: 10px;
}

.green-bg-and-text {
    display: inline-block;
    background-color: #EEFFE3 !important;
    color: #227F5B !important;
    font-size: 14px;
    line-height: 1.3;
    padding-left: 5px;
    padding-right: 5px;
}

.red-bg-and-text {
    display: inline-block;
    background-color: #F0DBDB !important;
    color: #B04748 !important;
    font-size: 14px;
    line-height: 1.3;
    padding-left: 5px;
    padding-right: 5px;
    }

.gray-bg-and-dark-text {
    display: inline-block;
    background-color: #cecfd0 !important;
    font-size: 14px;
    line-height: 1.3;
    padding-left: 5px;
    padding-right: 5px;
}

.text-danger-me {
    color: #A52D2E !important;
}

.text-green {
    color: #227F5B !important;
}


/************ Modals ***************/
.modal-primary {
    background-color: #deecff !important;
    color: #4692FF !important;
    border: #deecff !important;
}

.modal-primary label {
    color: #4692FF !important; /* Χρώμα του label */
}

.modal .modal-cancel-btn {
    background: white;
    color: #5646FF;
}

.modal .modal-cancel-btn:hover {
    background: #46EEFF;
    color: white;
}


.modal-body .form-label {
    color: black;
    display: block !important;
    font-size: 1.1rem;
    font-weight: 500;
    padding-bottom: 0.5rem;
    
    /* font-style: italic !important; */
    /*opacity: 0.6 !important;*/ /* Reduce opacity (0.0 to 1.0 scale) */
    /*font-size: 1.1rem; *//* Increase font size */
    /*font-weight: bold;  Make the text bold */
   /* color: #495057;  Dark text color */
    /*margin-bottom: 0.5rem;*/ /* Add spacing below the label */
    /*display: block;  Ensure label is a block-level element */
}


/***********  Breadcrumb css****************/
.custom-breadcrumb {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #666;
}

.custom-breadcrumb a {
    text-decoration: none;
    color: #ECECEC;
    transition: color 0.2s ease-in-out;
}

.custom-breadcrumb a:hover {
    color: #0056b3;
}

.separator {
    margin: 0 8px;
    color: #999;
}

.current {
    font-weight: bold;
    color: #333;
}


.profile-card {
    width: 100%;
    margin: auto;
    padding: 30px;
    background: white;
    border-radius: 10px;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
}
.profile-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
}
.btn-primary-custom {
    background-color: #a31e22;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 5px;
}
.label {
    font-weight: bold;
    color: #777;
    font-size: 14px;
}
.value {
    font-weight: bold;
    font-size: 16px;
}
.highlight-badge {
    background-color: #fff3cd;
    color: #000;
    font-weight: bold;
    padding: 4px 10px;
    border-radius: 5px;
}

.my-ul-list {
    list-style-type: none;
    background-color: #adb7ba;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    z-index: 1;
    cursor: pointer;
}

/***** Pagination Start ******************/

.pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
}
.page-item {
    margin-right: 5px;
}
.page-item.active .page-link {
    background-color: #4692FF;
    border-color: #4692FF;
    color: white !important
}
.page-link {
    color: #4692FF !important;
    font-weight: normal;
}
.page-link:hover {
    background-color: #4692FF !important;
    color: white !important;
    opacity: 0.5;
}
.page-item.disabled .page-link {
    color: #ccc;
}

/***** Pagination End ******************/

.login-input {
    color: #4692FF !important;
}


.login-input::placeholder {
    color: #4692FF !important;
}

.login-input:-webkit-autofill {
    -webkit-text-fill-color: black !important; /* Prevents autofill from overriding text color */
    caret-color: #4692FF; /* Ensures cursor color stays consistent */
    transition: background-color 5000s ease-in-out 0s; /* Prevents quick revert */
}

.my-input:-webkit-autofill::first-line {
    color: white !important; /* Ensures text color changes */
}

/* For Firefox */
.my-input:autofill {
    background-color: yellow !important;
    color: black !important;

/****************  Global spinner overlay (START) ******/
#spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7); /* Ημιδιαφανές λευκό background */
    backdrop-filter: blur(5px); /* Θόλωμα του background */
    display: none; /* Κρυφό αρχικά */
    z-index: 1050; /* Υψηλό z-index για να καλύπτει τα πάντα */
}

/****************  Global spinner overlay (END) ******/

/******  Icons for add button  and sorting (START)  *********************/
.button-icon,
.button-add-icon {
    width: 14px !important;  /* Set the width of the image (adjust as needed) */
    height: 14px !important; /* Set the height of the image (adjust as needed) */
    margin-right: 6px !important; /* Adjust the spacing between the icon and the text */
    vertical-align: middle !important; /* Ensure the icon aligns vertically with the text */
}



@media (min-width: 768px) {
    .offcanvas-end.offcanvas-wide {
    width: 900px !important;
    max-width: 100vw !important;
    }

}
@media (min-width: 1200px) {
    .offcanvas-end.offcanvas-wide {
    width: 900px !important;
    max-width: 100vw !important;
    }

}
.offcanvas-end.offcanvas-wide {
  background-color: lightyellow;
}


  .custom-tabs {
    display: flex;
    gap: 1.5rem;
    border-bottom: 2px solid #ddd;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
  }

  .tab-item {
    cursor: pointer;
    font-weight: 600;
    color: #333;
    position: relative;
    padding-bottom: 0.25rem;
    text-decoration: none;
  }

  .tab-item.active {
    color: #2da29b;
  }

  .tab-item.active::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #2da29b;
    border-radius: 2px;
  }

  .custom-cursor {
    cursor: pointer;
  }

@media (max-width: 768px) {
  .mobile-profile-dropdown-btn {
    padding-top: 1.2rem !important;
    padding-bottom: 1.2rem !important;
    font-size: 1.2rem !important;
    min-height: 64px;
    background-color: #e4fcf9 !important;
    border-radius: 12px;
  }
  .dropdown-menu .dropdown-item {
    padding-top: 1rem;
    padding-bottom: 1rem;
    font-size: 1.1rem;
  }
}


