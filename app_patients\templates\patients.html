{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}
{% block title %}{{ page_title }}{% endblock %}
{% block content %}
    <div class="container-fluid">
        <h2 class="mb-0">{{ page_title }}</h2>
        <div class="d-flex justify-content-end align-items-center gap-3">
            <button class="btn custom-columns-btn"
                    type="button"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#columnSelectorOffcanvas"
                    aria-controls="columnSelectorOffcanvas"
                    id="columnSelectorBtn">
                <i class="bi bi-layout-three-columns me-2"></i>
                Προσαρμογή Στηλών
            </button>
            <button class="add-btn m-4"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#addModalId">
                {{ add_button_label }}
                <i class="bi bi-plus fs-2"></i>
            </button>
        </div>
        <!-- Dynamic Table -->
        {% include 'include_files/data_table_app_patients.html' with columns=columns filters=filters table_id=table_id model_name=model_name %}
        <!-- Modals -->
        {% include 'modals/delete_object_modal_app_patients.html' with modal_id='deleteModal' model_name=model_name delete_url=delete_url %}
        {% include 'modals/add_edit_modal_app_patients.html' with modal_id='addModalId' modal_title=add_modal_title form_id='add_form_id' form_fields=form_fields model_name=model_name mode='add' create_update_url=create_update_url %}
        {% include 'modals/add_edit_modal_app_patients.html' with modal_id='editModalId' modal_title=edit_modal_title form_id='edit_form_id' form_fields=form_fields model_name=model_name mode='edit' create_update_url=create_update_url %}
        {% include 'include_files/column_selector_app_patients.html' with columns=columns table_id=table_id %}
    </div>
{% endblock %}
{% block js %}
    <script src="{% static 'js/app_patientsCtrl.js' %}"></script>
    <script src="{% static 'js/toast_messages.js' %}"></script>
    <script src="{% static 'js/column_selector_app_patients.js' %}"></script>
{% endblock %}
