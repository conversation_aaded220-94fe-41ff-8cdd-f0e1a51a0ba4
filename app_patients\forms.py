from django import forms
from .models import MedicalNote
from app_prescriptions.models import Prescription
from django.utils.safestring import mark_safe
from .models import Patient

class MedicalNoteForm(forms.ModelForm):
    class Meta:
        model = MedicalNote
        fields = ['note']
        widgets = {
            'note': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': 'Πληκτρολογήστε την ιατρική συμβουλή...'
            }),
        }

class PrescriptionForm(forms.ModelForm):
    prescriber_name = forms.ChoiceField(
        choices=[],
        label='Συνταγογραφών',
        widget=forms.Select(attrs={'class': 'form-select rounded-pill'})
    )

    class Meta:
        model = Prescription
        fields = ['barcode_number', 'prescriber_name', 'pharmacy_name']
        labels = {
            'barcode_number': 'Αριθμός Barcode',
            'pharmacy_name': 'Φαρμακείο',
        }
        widgets = {
            'barcode_number': forms.TextInput(attrs={'class': 'form-control  rounded-pill'}),
            'pharmacy_name': forms.TextInput(attrs={'class': 'form-control  rounded-pill'}),
        }

    # def __init__(self, *args, **kwargs):
    #     super().__init__(*args, **kwargs)
    #     # ✅ Add Bootstrap label class
    #     for field_name, field in self.fields.items():
    #         field.widget.attrs['class'] += ' fw-bold'  # You can remove if not needed
    #         field.label_suffix = ''  # optional: remove colon
    #         field.widget.attrs['placeholder'] = self.fields[field_name].label

    # def as_div(self):
    #     """Render form fields with custom label class"""
    #     return mark_safe(''.join([
    #         f'''
    #         <div class="mb-3">
    #             <label for="id_{name}" class="form-label">{field.label}</label>
    #             {field}
    #         </div>
    #         ''' for name, field in self.fields.items()
    #     ]))
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.label_suffix = ''  # ✅ Remove the colon from all labels
         # 🔽 Dynamically load distinct doctor names from Patient records
        prescriber_names = Patient.objects.values_list('prescriber_name', flat=True).distinct()
        self.fields['prescriber_name'].choices = [('', 'Επιλέξτε συνταγογράφο')] + [
            (name, name) for name in prescriber_names if name
        ]

