/* Ensure Manrope is used across sidebar */

/* ✅ Sidebar container (DESKTOP ONLY) */
/* .sidebar-container {
    background-color: #dffffc;
    border: 1px solid #6AC7BA;
    max-height: 80vh;
    padding: 12px;
    display: block;
    border-radius: 40px;
    margin: 10px 10px;
} */

@media (max-width: 768px) {
    .sidebar-container {
        display: none !important; /* Hide in mobile */
    }
}

/* ✅ Inner sidebar content */
.sidebar {
    width: 220px;
    padding: 0.5rem;
    background-color: #dffffc;
    height: 100%;
}

/* ✅ Mobile Offcanvas Sidebar */
@media (max-width: 768px) {
    .offcanvas .sidebar {
        padding: 1.2rem;
        border-radius: 0;
        background-color: #dffffc;
        height: 100%;
    }
}

/* ✅ Logo Styling */
.logo-container {
    text-align: start;
    padding: 0.3rem;
}
.sidebar-logo {
    max-height: 48px;
}
@media (max-width: 768px) {
    .sidebar-logo {
        max-height: 50px;
    }
}

/* ✅ Navigation */
.custom-sidebar-nav {
    list-style: none;
    padding-left: 0;
    margin: 0;
}
.custom-sidebar-nav .nav-item {
    margin-bottom: 10px;
}
.custom-sidebar-nav .nav-link {
    padding: 12px 4px;
    font-size: 1rem;
    display: flex;
    align-items: center;
    color: #051F1B;
    border-radius: 12px;
    transition: background-color 0.3s ease-in-out;
    text-decoration: none;
    margin-bottom: 10px;
    font-weight: 600;
}
.custom-sidebar-nav .nav-link.active,
.custom-sidebar-nav .nav-link:hover {
    background-color: #b7ede9;
    color: #000;
}

/* ✅ Icons */
.custom-icon {
    width: 26px;
    height: 26px;
    object-fit: contain;
    vertical-align: middle;
    margin-right: 18px;
}
@media (max-width: 768px) {
    .custom-icon {
        width: 24px;
        height: 24px;
    }
}

/* ✅ User block & logout */
.sidebar-user-block {
    margin-top: auto;
}
.nav-user-name {
    font-size: 1rem;
    font-weight: 500;
    color: #051F1B;
    margin-left: 5px;
    line-height: 1;
}

.nav-item.dropdown .nav-link {
    padding: 16px 10px;
    font-size: 1rem;
    color: #051F1B;
    border-radius: 12px;
    /* margin-top: 20px; */
    transition: background-color 0.3s ease-in-out;
}

.nav-item.dropdown .nav-link:hover {
    background-color: #b7ede9;
    color: #000;
}

/* Only affect submenu under Ρυθμίσεις */
.settings-submenu .nav-item {
    margin-bottom: 0;
}

.settings-submenu .nav-link {
    font-size: 1rem; /* smaller font */
    padding-top: 2px;
    padding-bottom: 2px;
    padding-left: 0.5rem;
    margin-left: 1rem;
    color: #051F1B;
}

.settings-submenu .nav-link:hover {
    background-color: #b7ede9;
    color: #000;
    /* border-radius: 4px; */
}

/* Logout button block */
.sidebar-logout-block {
    padding: 1rem 0.75rem;
}
.logout-button {
    background-color: #dffffc;
    border: 1px solid #6AC7BA;
    color: #051F1B;
    font-weight: 600;
    font-size: 1rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-align: left;
    padding: 10px 16px;
    cursor: pointer;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
}
.logout-button:hover {
    background-color: #b7ede9;
    color: #000;
}
.logout-text {
    font-family: 'Manrope', sans-serif;
}

/* Adjust for mobile */
@media (max-width: 768px) {
    .custom-sidebar-nav .nav-link {
        font-size: 1rem;
        padding: 12px 14px;
    }
    .nav-user-name,
    .logout-button {
        font-size: 0.95rem;
    }
}
