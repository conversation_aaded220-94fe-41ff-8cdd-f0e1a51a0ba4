
import random
import os
import sys
import traceback
current_dir = os.getcwd()
sys.path.append(current_dir)
#sys.path.append(f'{current_dir}/smart_college')
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)
sys.path.append('/opt/airos')
#sys.path.append('/opt/smart_college/smart_college')
#sys.path.append('/opt/smart_college/smart_college/smart_college')

from local_settings import k
from utilities import utils
#from utilities import utils_db
from utilities.crypto import Crypto
from utilities.messages import Messages
from utilities.status_code import StatusCode


messages_obj = Messages()
status_code_obj = StatusCode()


class TokenManager:
    
    def create_token(self, token_len):
        s = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!"#$%&()*+,-./:;<=>?@[]^_|~0123456789'
        len_of_s = len(s)
        token = ''
        for i in range(0, token_len):
            pos = random.randint(0, len_of_s - 1)
            token += s[pos]
        # print(f'{len(token)} {token}')
        return token

    def get_model_instance_by_email_token(self, model, email, token, first_instance_only=False):
        if first_instance_only:
            try:
                # print('model ', model)
                return model.objects.filter(token=token, email=email)[0]
            except Exception as e:
                traceback.print_exc()
                return None
        else:
            try:
                return model.objects.filter(token=token, email=email)
            except Exception as e:
                traceback.print_exc()
                return []


    def get_model_instance_by_username_token(self, model, username, token, first_instance_only=False):
        if first_instance_only:
            try:
                query_set = model.objects.filter(token=token, username=username)
                if query_set:
                    return query_set[0]
                else:
                    return None
            except Exception as e:
                utils.exception_traceback_print()
                return None
        else:
            try:
                # print(token)
                # print(email)
                return model.objects.filter(token=token, username=username)
            except Exception as e:
                traceback.print_exc()
                return []


    def check_token_validation_in_login(self, token_db_model, token_in_login, username, en_or_gr):
        try:
            # print('token in login ',  token_in_login)
            # manage_token = TokenManager()
            if token_in_login:
                # print("check_token_validation_in_logintoken_in_login token ", token_in_login)
                success_dec, mes, dec_token = self.decod_token(token_in_login, k.p, k.S_32bytes)
                if success_dec:
                    # print(dec_token)
                    # print(username)
                    instance = self.get_model_instance_by_username_token(model=token_db_model, username=username, token=dec_token,
                                                                  first_instance_only=True)
                    if instance:
                        return True, messages_obj.valid_token.get(en_or_gr), 200
                    else:
                        return False, messages_obj.invalid_token.get(en_or_gr), status_code_obj.unauthorized_401
                else:
                    return False, mes, status_code_obj.not_acceptable_406
            else:
                return False, messages_obj.null_token.get(en_or_gr), status_code_obj.bad_request_400
        except Exception as e:
            # t = utilities.exception_traceback_str(e)
            traceback.print_exc()
            if f'{e}' == '':
                e = 'invalid token'
            return False, messages_obj.exception(e, en_or_gr), status_code_obj.not_acceptable_406

    def check_token_validation_without_login(self, token_without_login, en_or_gr):
        try:
            # manage_token = ManageToken()
            if token_without_login:
                # dec_token = self.decod_token(token_without_login, k.p, k.S_32bytes)
                success_dec, mes, dec_token = self.decod_token(token_without_login, k.p, k.S_32bytes)
                if success_dec:
                    # print('dec_token: ', dec_token)
                    # print('k.token  : ', k.token_without_login)
                    if dec_token == k.token_without_login:
                        return True, messages_obj.valid_token_without_login.get(en_or_gr), 200
                    else:
                        return False, messages_obj.invalid_token_without_login.get(
                            en_or_gr), status_code_obj.unauthorized_401
                else:
                    return False, mes, status_code_obj.not_acceptable_406
            else:
                return False, messages_obj.null_token_without_login.get(en_or_gr), status_code_obj.bad_request_400
        except Exception as e:
            traceback.print_exc()
            if f'{e}' == '':
                e = 'invalid token'
            print(f'eeee:O{e}O ')
            return False, messages_obj.exception(e, en_or_gr), status_code_obj.not_acceptable_406

    def refresh_token(self, db_token_model, enc_token_current, username, token_len, password_cryp, salt_cryp):
        try:
            token_new = self.create_token(token_len)
            enc_token_new = self.encode_token(token_new, password_cryp, salt_cryp)
            try:
                token_new_entry = db_token_model(token=token_new, username=username)
                token_new_entry.save()
            except Exception as e:
                utils.exception_traceback_print()
                print("failure: refresh_token() failed to refresh the token, returning the current token.")
                return enc_token_current  ##It fails to refresh token, returning the current token.
            try:
                self.delete_token_entry_by_token_username(db_token_model, enc_token_current, username, password_cryp,
                                                       salt_cryp)
                print('Success: refresh_token() returned a new token, deleting the current token successfully.')
                return enc_token_new  ## It returns a new token, deleting the current token successfully.
            except Exception as e:
                utils.exception_traceback_print()
                print('refresh_token() returned a new token but failed to delete the current token from db.')
                return enc_token_new  ## It returns a new token, but failed to delete the current token.
        except Exception as e:
            traceback.print_exc()
            print("failure: refresh_token() failed to refresh the token, returning the current token.")
            return enc_token_current  ##It fails to refresh token, returning the current token.

    def encode_token(self, token, password, salt):
        crypto = Crypto()
        return crypto.encrypt_str(token, password, salt)

    def decod_token(self, encoded_token, password, salt):
        try:
            crypto = Crypto()
            dec_str = crypto.decrypt_str(encoded_token, password, salt)
            return True, '', dec_str
        except Exception as e:
            traceback.print_exc()
            if f'{e}' == '':
                e = 'invalid token'
            return False, messages_obj.exception(e, 'en'), ''

    def delete_token_entry_by_token_email(self, db_token_model, enc_token, email, password, salt):
        try:
            token = self.decod_token(enc_token, password, salt)
            instance_list = self.get_model_instance_by_email_token(model=db_token_model, email=email, token=token,
                                                                   first_instance_only=False)
            # instance_list = table_model.objects.filter(email=email)
            for inst in instance_list:
                inst.delete()
        except Exception as e:
            traceback.print_exc()
            # logs.write_error_logs(traceback.format_exc())

    def delete_token_entry_by_token_username(self, db_token_model, enc_token, username, password, salt):
        try:
            token = self.decod_token(enc_token, password, salt)
            instance_list = self.get_model_instance_by_username_token(model=db_token_model, username=username, token=token,
                                                                   first_instance_only=False)
            # instance_list = table_model.objects.filter(email=email)
            for inst in instance_list:
                inst.delete()
        except Exception as e:
            traceback.print_exc()




# def print_token_pair(token_len=64):
#     a = ManageToken()
#     token = a.create_token(token_len=token_len)
#     enc = a.encode_token(token, k.p, k.S_32bytes)
#     dec = a.decod_token(enc, k.p, k.S_32bytes)
#     print(f'token: {len(token)} {token}\nenc: {len(enc)} {enc}\ndec: {len(dec)} {dec}')

# print_token_pair()
