{% load static %}
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="Doctor Home Care CRM">
        <title>
            {% block title %}Doctor Home Care CRM{% endblock %}
        </title>
        <!-- Favicon -->
        <link rel="icon"
              type="image/x-icon"
              href="{% static 'images/doctor-favicon.png' %}">
        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;600&display=swap"
              rel="stylesheet">
        <!-- Bootstrap & Icons -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"
              rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons/font/bootstrap-icons.css"
              rel="stylesheet">
        <!-- Quill Editor -->
        <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
        <!-- Flatpickr CSS -->
        <link rel="stylesheet"
              href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
        <!-- Custom CSS -->
        <link rel="stylesheet" href="{% static 'css/global.css' %}">
        <link rel="stylesheet" href="{% static 'css/buttons.css' %}">
        <link rel="stylesheet" href="{% static 'css/menu.css' %}">
        <style>
        body {
            font-family: 'Manrope', sans-serif;
        }

        .logo-header-img {
            height: 60px;
        }

        .sidebar-container {
            background-color: #dffffc;
            border: 1px solid #6AC7BA;
            /* max-height: 120vh; */
            padding: 12px;
            display: block;
            border-radius: 20px;
            margin: 10px 10px;
            /* width: 320px; */
        }


        .main-content {
            flex: 1;
            padding: 1.5rem;
        }

        /* Hide the desktop sidebar on mobile */
        @media (max-width: 768px) {
            .sidebar-container {
                display: none !important;
            }
        }

        /* Optional fix for offcanvas to take full height */
        .offcanvas {
            height: 100vh;
        }
        .offcanvas-body {
            padding: 0.5rem;
            overflow-y: auto;
        }
        .dropdown-item:active {
            background-color: #E4FCF9 !important; /* or any color you prefer */
            color: inherit !important; /* keep text color normal */
        }
        </style>
        {% block css %}{% endblock %}
    </head>
    <body>
        <!-- ✅ Global Header -->
        <header class="bg-white p-2 d-flex justify-content-between align-items-center">
            <img src="{% static 'images/logo.png' %}"
                 alt="Logo"
                 class="logo-header-img">
            <button class="btn btn-outline-dark d-md-none mt-3"
                    type="button"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#mobileSidebar">
                <i class="bi bi-list"></i>
            </button>
            <!-- Profile Dropdown (Desktop only) -->
            <div class="dropdown d-none d-md-block">
                <button class="btn nav-link d-flex align-items-center btn-prof-hover px-3 py-1 rounded-3"
                        type="button"
                        data-bs-toggle="dropdown"
                        aria-expanded="false">
                    <i class="bi bi-person-circle fs-4 me-2"></i>
                    {{ user.first_name }} {{ user.last_name }}
                    <i class="bi bi-chevron-down fs-6 ms-2"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="/profile/">
                            <i class="bi bi-person-lines-fill fs-5 me-2"></i> Προφίλ
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{% url 'logout' %}">
                            <i class="bi bi-box-arrow-right fs-5 me-2"></i> Αποσύνδεση
                        </a>
                    </li>
                </ul>
            </div>
        </header>
        <!-- ✅ Layout Body -->
        <div class="d-flex">
            <!-- ✅ Sidebar (Desktop only) -->
            <aside class="sidebar-container d-none d-md-block">
                {% include "menu.html" %}
            </aside>
            <!-- ✅ Sidebar (Mobile - Offcanvas) -->
            <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileSidebar">
                <div class="offcanvas-header">
                    <!-- <h5 class="offcanvas-title">Μενού</h5> -->
                    <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
                </div>
                <div class="offcanvas-body">{% include "menu.html" %}</div>
                <!-- Mobile Profile Dropdown (Only on small screens) -->
                <!-- Mobile Profile Dropdown (Only on small screens) -->
            </div>
            <!-- ✅ Main Content -->
            <main class="main-content w-100">
                {% block content %}{% endblock %}
            </main>
        </div>
        <!-- Scripts -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
        <!-- Flatpickr JS + Greek locale -->
        <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
        <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/gr.js"></script>
        <!-- Custom JS -->
        <script src="{% static 'js/global.js' %}"></script>
        <script src="{% static 'js/genericCtrl.js' %}"></script>
        {% block js %}
        {% endblock %}
    </body>
</html>
