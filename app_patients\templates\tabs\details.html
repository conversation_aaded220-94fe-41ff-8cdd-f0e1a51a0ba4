{% load custom_filters %}
<div class="border pt-4 px-2 rounded-4"
     style="max-height: 64vh;
            overflow-y: auto;
            overflow-x: hidden;
            position: relative;
            display: flex;
            flex-direction: column">
    <!-- FADE OVERLAY -->
    <div style="position: absolute;
                bottom: 5rem;
                left: 0;
                width: 100%;
                pointer-events: none;
                background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,1));
                z-index: 2"></div>
    <form method="POST"
          action="{% url 'createUpdatePatient' %}"
          enctype="multipart/form-data">
        {% csrf_token %}
        <input type="hidden" name="tablename" value="Patient">
        {% if patient.id %}<input type="hidden" name="id" value="{{ patient.id }}">{% endif %}
        <div class="row">
            {% for field in form_fields %}
                <div class="col-md-{{ field.width|default:'4' }} mb-3">
                    <label class="form-label text-secondary fw-bold">{{ field.label }}</label>
                    {% if field.id == 'amka' %}
                        <input type="text"
                               name="{{ field.id }}"
                               class="form-control"
                               value="{{ patient|get_item:field.id }}"
                               inputmode="numeric"
                               pattern="\d*"
                               maxlength="11"
                               oninput="this.value = this.value.replace(/\D/g, '')"
                               {% if field.required %}required{% endif %}
                               placeholder="{{ field.placeholder|default:'' }}">
                    {% elif field.type == 'text' or field.type == 'email' %}
                        <input type="{{ field.type }}"
                               name="{{ field.id }}"
                               class="form-control"
                               value="{{ patient|get_item:field.id }}"
                               {% if field.required %}required{% endif %}
                               placeholder="{{ field.placeholder|default:'' }}">
                    {% elif field.type == 'date' %}
                        <input type="date"
                               name="{{ field.id }}"
                               class="form-control"
                               value="{{ patient|get_item:field.id|date:'Y-m-d' }}"
                               {% if field.required %}required{% endif %}>
                    {% elif field.type == 'select' %}
                        <select name="{{ field.id }}"
                                class="form-select"
                                {% if field.required %}required{% endif %}>
                            <option value="">Επιλέξτε...</option>
                            {% for opt in field.options %}
                                <option value="{{ opt.value }}"
                                        {% if opt.value == patient|get_item:field.id %}selected{% endif %}>
                                    {{ opt.label }}
                                </option>
                            {% endfor %}
                        </select>
                    {% elif field.type == 'textarea' %}
                        <textarea name="{{ field.id }}"
                                  class="form-control"
                                  rows="4"
                                  {% if field.required %}required{% endif %}>{{ patient|get_item:field.id }}</textarea>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
        <!-- Fixed Save Button Bar -->
        <div class="text-end"
             style="position: sticky;
                    bottom: 0;
                    background: rgb(254, 254, 254);
                    padding: 1.5rem 2rem;
                    margin-top: auto;
                    box-shadow: 0 -15px 25px -10px rgba(0, 0, 0, 0.08);
                    z-index: 10">
            <button type="submit" class="btn btn-dark rounded-pill px-4">Αποθήκευση</button>
        </div>
    </form>
</div>
