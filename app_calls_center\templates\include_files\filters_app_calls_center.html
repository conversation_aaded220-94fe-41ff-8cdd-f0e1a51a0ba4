{% load custom_filters %}
{% for filter in filters %}
    <div class="col-auto {{ filter.style.container|default:'mb-3 me-2' }}">
        <label for="{{ filter.id }}"
               class="{{ filter.style.label|default:'form-label text-secondary fw-bold' }}"
               style="font-size: {{ filter.style.label_size|default:'0.9rem' }};
                      margin-bottom: {{ filter.style.label_margin|default:'0.3rem' }}">{{ filter.label }}</label>
        {% if filter.type == 'select' %}
            <select id="{{ filter.id }}"
                    name="{{ filter.name }}"
                    class="{{ filter.style.input|default:'form-control' }}"
                    style="width: {{ filter.width|default:'auto' }};
                           {{ filter.style.input_style|default:'' }}">
                <option value="">Όλα</option>
                {% for option in filter.options %}
                    <option value="{{ option.value }}"
                            {% if request.GET|get_item:filter.name == option.value %}selected{% endif %}>
                        {{ option.label }}
                    </option>
                {% endfor %}
            </select>
        {% elif filter.type == 'date' %}
            <input type="date"
                   id="{{ filter.id }}"
                   name="{{ filter.name }}"
                   class="{{ filter.style.input|default:'form-control' }}"
                   value="{{ request.GET|get_item:filter.name|default_if_none:'' }}"
                   style="width: {{ filter.width|default:'auto' }};
                          {{ filter.style.input_style|default:'' }}">
        {% elif filter.type == 'text' %}
            <input type="text"
                   id="{{ filter.id }}"
                   name="{{ filter.name }}"
                   class="{{ filter.style.input|default:'form-control' }}"
                   value="{{ request.GET|get_item:filter.name|default_if_none:'' }}"
                   style="width: {{ filter.width|default:'auto' }};
                          {{ filter.style.input_style|default:'' }}">
        {% endif %}
    </div>
{% endfor %}
