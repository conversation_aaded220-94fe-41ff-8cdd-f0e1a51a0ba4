
### Services url:
https://services.airos.gr:443

### Requirements
    Python 3.12.1
    django 5.1.2
    cryptography 43.0.1
    pytz-2024.2
    xlrd-2.0.1
    django-cors-headers-4.5.0
    certbot-django-0.2.0
    psycopg2-2.9.9
    gunicorn-23.0.0

    #install requirements
    python -m pip install Django
    python -m pip install cryptography
    python -m pip install pytz
    python -m pip install xlrd
    python -m pip install django-cors-headers
    python -m pip install certbot-django
    python -m pip install  psycopg2
    python -m pip install gunicorn
    # Also see file requirements.txt
    pip freeze > requirements.txt


###  Virtual Environment
    #Create venv
    python3.12 -m venv .venv
    # Activate the Virtual Environment
    source .venv/bin/activate
    
    # For python 12 install pip in Venv:
    pip install --upgrade setuptools
    python -m ensurepip --upgrade
    
    # Check the installed packages to python 3.12
    pip freeze
    # Create a requirements.txt file:
    pip freeze > requirements.txt

    # Only the installed packages (not the dependencies)
    pip install pipdeptree
    pipdeptree --freeze --warn silence | Select-String -Pattern '^[a-zA-Z0-9-]+==' > requirements.txt

    
    # Install requirements (using file requirements.txt):
    #sudo pip install -r requirements.txt
    pip install -r requirements.txt
    # Verify the installation:
    pip list

    
    
    #Deactivate venv:
    deactivate

### Ensuring package installation to the specific Python environment (preferable when exist multiple Python versions)
    python -m pip install python_package

### install django [documentation](https://docs.djangoproject.com/), [installation](https://docs.djangoproject.com/en/5.1/topics/install/#installing-official-release)
    python -m pip install Django
    python -m django --version

### Creating a project [tutorial](https://docs.djangoproject.com/en/5.1/intro/tutorial01/)
	django-admin startproject xprices

### Creating an App [tutorial](https://docs.djangoproject.com/en/5.1/intro/tutorial01/)
    python xprices/manage.py startapp user_app

### Django REST framework [tutorial](https://www.django-rest-framework.org/tutorial/quickstart/)
	pip install djangorestframework

### Run django server
    python xprices/manage.py runserver 
    ### Or Changing the port
	python xprices/manage.py runserver 8002
    ### Or if you want to change the server’s IP, pass it along with the port
	python xprices/manage.py runserver 0.0.0.0:8001


### in settings.py:
` INSTALLED_APPS = [
    ...,   
    "corsheaders",
    ...,   
] `

` MIDDLEWARE = [
    ...,  
    "corsheaders.middleware.CorsMiddleware",  
    "django.middleware.common.CommonMiddleware",  
    ...,  
] `

` CORS_ALLOWED_ORIGINS = [
    "https://example.com",  
    "https://sub.example.com",  
    "http://localhost:8080",  
    "http://127.0.0.1:9000",  
]  `   


### MySQL 

#### add the admin to  xprices_db
    INSERT INTO auth_user (username, password, first_name, last_name, email, tel)
    VALUES ('john_doe', 'hashed_password', 'John', 'Doe', '<EMAIL>', '1234567890');
#### Create user in  MySQL
    CREATE USER 'new_username'@'localhost' IDENTIFIED BY 'password';
#### Change password in MySQL
    ALTER USER 'username'@'localhost' IDENTIFIED BY 'new_password';

#### Useful commands in MySQL
    # Logging in
    mysql -u username -p
    SHOW DATABASES;
    CREATE DATABASE database_name;
    USE database_name;
    SHOW TABLES;
    DESCRIBE table_name;
    SELECT * FROM table_name;
    CREATE TABLE users(
        id varchar(255) NOT NULL,
        name VARCHAR(100),               
        email VARCHAR(100) NOT NULL UNIQUE,
        mobile varchar(15) UNIQUE,               
        password varchar(255) NOT NULL,                       
        last_modified datetime DEFAULT CURRENT_TIMESTAMP,
        isactive bit(1),
        islocked bit(1),
        PRIMARY KEY     (id)
        );
    ALTER TABLE users MODIFY mobile varchar(15) UNIQUE;
    ALTER TABLE users MODIFY last_modified datetime DEFAULT CURRENT_TIMESTAMP;
    ALTER TABLE users MODIFY isactive bit(1);
    DELETE FROM table_name WHERE condition;
    DELETE FROM table_name WHERE id=1;
    # Updating data in a table
    UPDATE table_name SET column1 = value1, column2 = value2 WHERE condition;
    # Inserting data into a table:
    INSERT INTO table_name (column1, column2, ...) VALUES (value1, value2, ...);
    exit;
    DELETE FROM table_name WHERE primary_key_column = value;

#### Remove database in MySQL
    DROP DATABASE database_name;

#### To show ip and port of a database:
    status 
    # or
    SELECT SUBSTRING_INDEX(USER(), '@', -1) AS ip,  @@hostname as hostname, @@port as port, DATABASE() as current_database;


### Deploy django using Gunicorn and  Nginx server   

#### Install requirements
    sudo apt-get install nginx mysql-server python3-pip python3-dev libmysqlclient-dev ufw virtualenv


#### Setting up the firewall
	sudo apt-get install ufw
	sudo ufw default deny
	sudo ufw enable
	sudo ufw allow 8000
    sudo ufw status verbose


#### Activate venv, install requirements for django project, Make migrations and Apply migrations to  django project, collect static files and  run the development server for testing. Then deactivate the venv.
    python manage.py makemigrations an_app
	# or for all apps
	python xprices/manage.py makemigrations
	
    python manage.py migrate any_app
	# or for all apps
	python manage.py migrate
    
    # Collect static files (for production)
    python xprices/manage.py collectstatic

    python xprices/manage.py runserver 
    # Or Changing the port
	python xprices/manage.py runserver 8080
    # Or
	python xprices/manage.py runserver 0.0.0.0:8000

### Gunicorn:
#### Activate venv, install Guniconr, Running the followin command to indetify if gunicor run and then kill the process and exit the virtual environment to setting up Gunicorn.   
    (venv)$ pip install gunicorn
    (venv)$ gunicorn --bind 0.0.0.0:8001 --pythonpath /opt/xprices-django/xprices xprices.wsgi:application    
   
    /opt/xprices-django/.venv/bin/gunicorn --access-logfile /var/xprices/logs/gunicorn_access.log --error-logfile /var/xprices/logs/gunicorn_error.log --workers 3 --bind 0.0.0.0:8001 unix:/run/gunicorn.sock --pythonpath /opt/xprices-django/xprices xprices.wsgi:application
                                                                                                                                                                                                                                  

 # kill Gunicorn process and deactivate the venv
    deactivate
##### Create Gunicorn service and Socket (files in server_files directory)
    sudo nano /etc/systemd/system/xprices_gunicorn.service
    sudo systemctl enable /etc/systemd/system/xprices_gunicorn.service
    sudo systemctl start xprices_gunicorn.service
    sudo systemctl status xprices_gunicorn.service

    #In any code change
    sudo systemctl restart xprices_gunicorn.service


    sudo nano /etc/systemd/system/xprices_gunicorn.socket
    sudo systemctl enable /etc/systemd/system/xprices_gunicorn.socket
    sudo systemctl start xprices_gunicorn.socket
    sudo systemctl status xprices_gunicorn.socket

    #disable service and socket
    sudo systemctl disable gunicorn.service
    sudo systemctl disable gunicorn.socket

    #restart 
    sudo systemctl restart xprices_gunicorn.service
    sudo systemctl restart xprices_gunicorn.socket
    


### Setting up Nginx (file in server_files directory)

    sudo nano /etc/nginx/sites-available/xprices_config.conf

    #only one config file  must be placed in  /etc/nginx/sites-enabled (remove other files)
	sudo ln -s /etc/nginx/sites-available/xprices_config.conf /etc/nginx/sites-enabled
	sudo nginx -t

    # nginx must run in order to install certbot ssl  sertificate
	sudo systemctl restart nginx

    sudo systemctl status nginx
	

### Firewall: allow all ports necessary for nginx
	sudo ufw delete allow 8000
	sudo ufw allow 'Nginx Full' 
    sudo ufw status verboseη


### SSL
#### Install SSL using Certbot:
	 sudo apt install certbot python3-certbot-nginx -y
#### Creating SSL certificate:

    #nginx must be in running mode in order to install certbot ssl  sertificate
	sudo certbot --nginx -d xpricesbackend.bet-now.club

##### output in requesting certificate:
 ```
Account registered.
Requesting a certificate for xpricesbackend.bet-now.club

Successfully received certificate.
Certificate is saved at: /etc/letsencrypt/live/xpricesbackend.bet-now.club/fullchain.pem
Key is saved at:         /etc/letsencrypt/live/xpricesbackend.bet-now.club/privkey.pem
This certificate expires on 2024-09-28.
These files will be updated when the certificate renews.
Certbot has set up a scheduled task to automatically renew this certificate in the background.

Deploying certificate
Successfully deployed certificate for xpricesbackend.bet-now.club to /etc/nginx/sites-enabled/default
Congratulations! You have successfully enabled HTTPS on https://xpricesbackend.bet-now.club

- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
If you like Certbot, please consider supporting our work by:
 * Donating to ISRG / Let's Encrypt:   https://letsencrypt.org/donate
 * Donating to EFF:                    https://eff.org/donate-le
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
```


### Renew SSL certbot certificate:   
    sudo certbot renew --nginx
    #In problem It may need:
    sudo pip install idna
    # Also it may need to get the path of certbot in the system:
    which -a certbot
    sudo /bin/certbot renew --nginx
    #Finally, reload nginx
    sudo systemctl reload nginx
    sudo systemctl restart nginx




#### Configure server  (file in server_files directory)
    sudo nano /etc/nginx/sites-available/xprices_config.conf
    sudo nginx -t
	sudo systemctl restart nginx


### Colored terminal output
	python -m pip install "colorama >= 0.4.6"




### Useful Django code and commands
    #### Install Django (in venv, current version 5.1, 9/10/2024)
	python -m pip install Django
    
    #### For python 12 install pip: in Venv
	pip install --upgrade setuptools
	py -m ensurepip --upgrade

    ### Django version
	python -m django --version

    ### Creating a project
	django-admin startproject dromologitis

    ### The development server
	python dromologitis/manage.py runserver
    ### Or Changing the port
	python dromologitis/manage.py runserver 8080
    ### Or if you want to change the server’s IP, pass it along with the port
	python manage.py runserver 0.0.0.0:8000

    ### Creating an App
    python dromologitis/manage.py startapp dromologitis_app

    #### Create views in dromologitis_app/views.py. 
    from django.http import HttpResponse
	def index(request):
    	return HttpResponse("Hello, world. You're at the polls index.")

    # In the dromologits_app/urls.py file include the following code:
    from django.urls import path
	from . import views
	urlpatterns = [path("", views.index, name="index"),]

    # The next step is to point the root URLconf at the dromologitis_app.urls module. 
    # In dromologitis/urls.py, add an import for django.urls.include and insert an include() in the urlpatterns list, so you have:
		from django.contrib import admin
		from django.urls import include, path
		urlpatterns = [
    		path("dromologitis_app/", include("dromologitis_app.urls")),
    		path('admin/', admin.site.urls),
		]


    # Database setup
	#Open dromologitis/settings.py.
	If using SQLite:
			DATABASES = {
    			'default': {
        			'ENGINE': 'django.db.backends.sqlite3',
        			'NAME': BASE_DIR / 'db.sqlite3',
    			}
			}
	Else if using MySQL:
		'default': {
        	'ENGINE': 'django.db.backends.mysql',
        	'NAME': 'dromologitis_db',
			'USER': 'root',
        	'PASSWORD': 'sdfasdf',
        	'HOST': '127.0.0.1', #localhost
        	'PORT': '3306',
    }	

    pip install mysqlclient	
    python dromologitis/manage.py migrate

    ### Create Superuser
	python dromologitis/manage.py createsuperuser


    ## Django REST framework
	# Tutorial Link: https://www.django-rest-framework.org/tutorial/quickstart/
	pip install djangorestframework  


    # Creating a rest App
	python dromologitis/manage.py startapp dromologitis_rest_api_app

    # Serializers
	#Create a new module dromologitis/dromologitis_rest_api_app/serializers.py that we'll use for our data representations.
	from django.contrib.auth.models import Group, User
	from rest_framework import serializers
	class UserSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = User
        fields = ['url', 'username', 'email', 'groups']
	class GroupSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Group
        fields = ['url', 'name']

    #  Views in  dromoloigitis/dromologitis_rest_api_app/views.py
	from django.contrib.auth.models import Group, User
	from rest_framework import permissions, viewsets
	from tutorial.quickstart.serializers import GroupSerializer, UserSerializer
	class UserViewSet(viewsets.ModelViewSet):
    	"""
    	#API endpoint that allows users to be viewed or edited.
    	"""
    	queryset = User.objects.all().order_by('-date_joined')
    	serializer_class = UserSerializer
    	permission_classes = [permissions.IsAuthenticated]
	class GroupViewSet(viewsets.ModelViewSet):
    	"""
    	API endpoint that allows groups to be viewed or edited.
    	"""
    	queryset = Group.objects.all()
    	serializer_class = GroupSerializer
    	permission_classes = [permissions.IsAuthenticated]



    # Settings (dromologitis/settings.py)

    # Pagination (Pagination allows you to control how many objects per page are returned
	REST_FRAMEWORK = {
    	'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    	'PAGE_SIZE': 10
	}

    # Add 'rest_framework' to INSTALLED_APPS.
	INSTALLED_APPS = [
    	...
    	'rest_framework',
	]

    # Activating models
    #Add 'dromologitis_rest_api_app.apps.DromologitisRestApiAppConfig' to INSTALLED_APPS in dromologitis/settings.py


    # Run makemigrations, telling Django that you’ve made some changes to your models :
	python dromologitis/manage.py makemigrations dromologitis_rest_api_app
	# or 
	python dromologitis/manage.py makemigrations

    # See migratons, which are going to be run to change the database (see migrate command below): 
	#After running makemigrations, a file with migrations has been created in dromologitis_rest_api_app/migrations, open this file.

    # See the SQL commands that migration would run (see migrate command below)
	python manage.py sqlmigrate dromologitis_rest_api_app 0001 

    # Migration: Apply all migrations to dromologitis_rest_api_app (creating model tables or updating the db scheme) 
	python dromologitis/manage.py migrate dromologitis_rest_api_app
	# or 
	python dromologitis/manage.py migrate
    
    # Collect static files (for production)
    python xprices/manage.py collectstatic

    #Run server
	dromologitis/manage.py runserver
	python dromologitis/manage.py runserver 0.0.0.0:8000




### install python 3.12 on centOS 7
    sudo su 
    yum update 
    sudo yum groupinstall 'Development Tools'
    sudo yum install openssl-devel bzip2-devel libffi-devel sqlite-devel 
    wget https://www.python.org/ftp/python/3.12.1/Python-3.12.1.tgz 
    tar xzf Python-3.12.1.tgz 
    cd Python-3.12.1 
    ./configure --enable-optimizations 
    make -j 4
    ./configure
    make clean
    make altinstall 
    python3.12 --version 
    python3.12 -m ensurepip --upgrade 



### model filtering
    #Case-insensitive exact match search
    results = MyModel.objects.filter(field_name__iexact=search_term)
    # Case-insensitive partial match search
    results = MyModel.objects.filter(field_name__icontains=search_term)



### file permissions
    sudo chown codnext:codnext -R /var/dromologitis/

### Add persistent permissions to a dir, run the in each reboot of the server
    setfacl -R -d -m u:codnext:rwx /var/dromologitis
    setfacl -R -d -m g:www-data:rwx /var/dromologitis
    # maybe without -d
    setfacl -R -m u:codnext:rwx /var/dromologitis
    setfacl -R -m g:www-data:rwx /var/dromologitis
    #verify the permissions
    getfacl /var/xprices

### Git - GitLab 
    #Git global setup
    git config --global user.name "Panagiotis Kouris"
    git config --global user.email "<EMAIL>"

    #Push an existing folder (Gitlab)
    cd xprices-django
    git init --initial-branch=main
    git remote add origin https://gitlab.com/pngkouris.codnext/xprices-django.git
    git add .
    git commit -m "Initial commit"
    git push --set-upstream origin main

    #Git clone (Gitlab)
    git clone https://oauth2:[user_token]@gitlab.com/codnext/xprice-django.git

    #set remote repository (Gitlab)
    git remote set-url origin https://oauth2:[user_token]@gitlab.com/codnext/xprices-django.git

    ### remove git cache
    # Remove file or folder
    git rm -r --cached <file_or_folder>
    # Remove the whole cashed files or folders
    git rm -r --cached .
    git add .
	git commit -m "Removed cached files"
	git push

    


### Push Notifications
    pip install firebase-admin 
    use push_notifications_manager in utils
    use key json of the firebase project (not app, e.g., e-symboulos-firebase-project)
    Add key to k.py and add var in params to key


### Delete (Django python)
    def delete(request):
    try:
        if request.method == 'POST':
            form_data = {key: value.strip() for key, value in request.POST.items()}
            id = form_data.get('obj_id')
            tablename = form_data.get('tablename')
            print("delete " + tablename + " with id: " + id)
            model_class = MODEL_MAPPING.get(tablename)
            if model_class:
                if id:  # Delete
                    obj = model_class.objects.get(id=id)
                    obj.delete()
                    return redirect('get-users')  # Replace with your list view name
        else:
            messages.error(request, 'Invalid request method.')
            return redirect('get-users')


    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred!'})


### Delete (bootstrap button)
    button
    <button class="btn btn-danger btn-sm"
        onclick="openDeleteModal('User', '{{ user.id }}', '{{ user.email }}')"><i
        class="fas fa-trash-alt"></i> Διαγραφή
    </button>

### Delete (bootstrap modal)
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom: none">
                    <div class="d-flex justify-content-between w-100">
                        <h5 class="modal-title" id="deleteModalLabel"><strong>Διαγραφή Χρήστη</strong></h5>
                        <button type="reset" class="custom-close-btn" data-bs-dismiss="modal" aria-label="Close">✖</button>
                    </div>
                </div>
                <div class="modal-body">
                    <form method="POST" action="{% url 'delete' %}" style="display:inline;">
                        {% csrf_token %}
                        <p>Είστε σίγουροι για τη διαγραφή του χρήστη με email: <i id="value1"></i> ;</p>
                        <input type="hidden" id="obj_id" name="obj_id" value="">
                        <input type="hidden" id="tablename" name="tablename" value="">
                        <div class="text-end">
                            <button type="submit" class="btn btn-danger btn-sm">
                                <i class="fas fa-trash-alt"></i> Διαγραφή
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

### Delete (javascript)
    <script>
        // Function to open the modal and populate it with parameters
        function openDeleteModal(tablename, id, value1) {
            // Set modal content dynamically
            document.getElementById('tablename').value = tablename;
            document.getElementById('obj_id').value = id;
            document.getElementById('value1').textContent = value1;

            // Use Bootstrap's modal API to show the modal
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
           }
    </script>

### CreateUpdate (django python)
    def createUpdateUser(request):
    try:
        if request.method == 'POST':
            # Get all form data dynamically
            form_data = {key: value.strip() for key, value in request.POST.items()}

            # Check if the 'id' field is present in the form data
            id = form_data.get('id', None)
            tablename = form_data.get('tablename')
            model_class = MODEL_MAPPING.get(tablename)

            if id:  # Update an existing Object
                obj = model_class.objects.get(id=id)

                # Update the user's information
                for key, value in form_data.items():
                    if hasattr(obj, key) and key != 'id':  # Skip setting the 'id' field
                        setattr(obj, key, value)

                # Handle password separately if it's provided
                if 'password' in form_data and form_data['password']:
                    obj.set_password(form_data['password'])

                obj.save()
                return redirect('get-users')
                # return JsonResponse({'status': 'success', 'message': 'User updated successfully!'})

            else:  # Create a new Object
                # Check if email already exists only when creating a new user
                if model_class.objects.filter(email=form_data.get('email')).exists():
                    return JsonResponse({'status': 'error', 'message': 'Email already exists!'})

                table_fields = [field.name for field in model_class._meta.get_fields()]

                filtered_data = {
                    key: value for key, value in form_data.items()
                    if key in table_fields and key != 'id'  # Exclude 'id'
                }

                new_obj = model_class.objects.create(**filtered_data)

                # Handle password separately if it's provided
                if 'password' in form_data:
                    new_obj.set_password(form_data['password'])
                new_obj.save()
                return_list = model_class.objects.all()
                context = {
                    tablename + 'list': return_list
                }
                return render(request, 'users.html', context)
                # return JsonResponse({'status': 'success', 'message': 'User added successfully!'})

    except model_class.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': 'User not found!'})

    except Exception as e:
        traceback.print_exc()
        logs_manager.write_error_logs(traceback.format_exc())
        return JsonResponse({'status': 'error', 'message': 'An unexpected error occurred!'})

### Default page with menu navbar
    {% extends "base.html" %}

    {% load static %}
    {% block css %}
    <link rel="stylesheet" href="{% static 'css/settings.css' %}">
    {% endblock css %}
    
    {% block title %}Settings{% endblock %}
    
    {% block content %}
    {% include "header.html" %}
    
    <script src="{% static 'js/global.js' %}"></script>
    
    <div class="container-fluid" style="margin-top: 48px;">
    
        <!-- Left Menu -->
        <nav class="col-md-2 col-sm-3 bg-dark text-light fixed-menu">
            {% include "menu.html" %}
        </nav>


        <div class="col-md-10 col-sm-9 offset-md-2 offset-sm-3 pb-3">
    
    
            <div class="d-flex justify-content-end align-items-center py-2">
    <!--            !-&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45; Breadcrumb -&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;!-->
    <!--            <div class="d-flex">-->
    <!--                <div class="custom-breadcrumb">-->
    <!--                    <a href="/viografika/" class="text-dark"><strong>ΒΙΟΓΡΑΦΙΚΑ</strong></a>-->
    <!--                    <span class="separator">›</span>-->
    <!--                    &lt;!&ndash;                    <a href="#">Library</a>&ndash;&gt;-->
    <!--                    &lt;!&ndash;                    <span class="separator">›</span>&ndash;&gt;-->
    <!--                    <span class="current gray">{{viografiko.first_name}} {{viografiko.last_name}}</span>-->
    <!--                </div>-->
    <!--            </div>-->
                <button class="btn btn-danger">Προσθήκη SMS Template <i class="fas fa-plus me-2"></i></button>
    <!--            <img-->
    <!--                    src="{% static 'images/ergazomenoi/Untitled-1.png' %}" alt="Icon"-->
    <!--                    class="button-add-icon">-->
            </div>
            {% include 'nav_settings.html' with general_settings=general_settings %}
            <div class="d-flex align-items-center">
                <div class="card-body w-100">
                    <div class="container-fluid">
                        <div class="row profile-card">
                            <!-- Profile Section -->
    
                            <!-- Details Section -->
    
    <!--                        <div class="col-md-1">-->
    <!--                            <div class="row">-->
    <!--                                &lt;!&ndash; Edit Icon &ndash;&gt;-->
    <!--                                <div class="col-md-12 text-end">-->
    <!--                                    <button class="btn btn-sm me-2" title="Edit"-->
    <!--                                            onclick="openEditModal('Resume', {{viografiko.id}} , 'editViografikaModal')">-->
    <!--                                        <i class="bi bi-pencil-square text-dark"></i>-->
    <!--                                    </button>-->
    <!--                                </div>-->
    <!--                            </div>-->
    <!--                        </div>-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-----------------------------------Modals------------------------------------------------------------->
    
    <div class="modal modal-lg fade" id="editViografikaModal" tabindex="-1" aria-labelledby="editViografikaModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom: none">
                    <div class="d-flex justify-content-between w-100">
                        <h5 class="modal-title" id="editViografikaModalLabel"><strong>Επεξεργασία Βιογραφικού</strong></h5>
                        <button type="reset" class="custom-close-btn" data-bs-dismiss="modal" aria-label="Close">✖</button>
                    </div>
                </div>
                <div class="modal-body p-5">
                    <form method="POST" action="{% url 'createUpdateViografiko' %}" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="mb-1"><input type="hidden" id="id_edit" name="id" value="{{viografiko.id}}"></div>
                        <!--<p><strong>Όνομα:</strong> <span id="first_name_edit"></span></p>-->
                        <!--                <p><strong>Επώνυμο:</strong> <span id="last_name_edit"></span></p>-->
                        <!--                <p><strong>Email:</strong> <span id="email_edit"></span></p>-->
                        <!--                <p><strong>Ημερομηνία Γέννησης:</strong> <span id="imerominia_gennisis_edit"></span></p>-->
                        <!--                <p><strong>Πατρώνυμο:</strong> <span id="patrwnymo_edit"></span></p>-->
                        <!--                <p><strong>Κινητό:</strong> <span id="kinhto_edit"></span></p>-->
                        <!--                <p><strong>IBAN:</strong> <span id="iban_edit"></span></p>-->
                        <div class="row">
                            <div class="col-md-6 mb-1">
                                <label for="first_name_edit" class="form-label required">Όνομα </label>
                                <input type="text" id="first_name_edit" name="first_name" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-1">
                                <label for="last_name_edit" class="form-label required">Επώνυμο </label>
                                <input type="text" id="last_name_edit" name="last_name" class="form-control" required>
                            </div>
                            <!--                        <div class="col-md-2 mb-1">-->
                            <!--                            <label for="fotografia" class="form-label">Φωτογραφία</label>-->
                            <!--                            <input type="file" class="form-control" id="fotografia" name="fotografia">-->
                            <!--                        </div>-->
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-1">
                                <label for="kinhto_edit" class="form-label">Κινητό </label>
                                <input class="form-control" id="kinhto_edit" type="text" name="kinhto" required>
                            </div>
                            <div class="col-md-6 mb-1">
                                <label for="email_edit" class="form-label required">Email </label>
                                <input class="form-control" type="email" id="email_edit"
                                       name="email"
                                       required>
                            </div>
                        </div>
                        <div class="row">
                            <!--<div class="col-md-6 mb-1">
                                <label for="hlikia" class="form-label">Ηλικία</label>
                                <input class="form-control" type="number" id="hlikia" name="hlikia" disabled>
                            </div>-->
                            <div class="col-md-2 mb-1 me-5">
                                <label for="proyphresia_edit" class="form-label">Προϋπηρεσία(χρ.)</label>
                                <input class="form-control" type="number" id="proyphresia_edit" name="proyphresia"
                                       step="0.5">
                            </div>
                            <div class="col-md-4 mb-1 me-2">
                                <label for="imerominia_gennisis_edit" class="form-label required">Ημερομηνία
                                    Γέννησης </label>
                                <input type="date" class="form-control custom-date-input" id="imerominia_gennisis_edit"
                                       name="imerominia_gennisis" required>
                            </div>
                            <div class="col-md-4 mb-1">
                                <label for="patrwnymo_edit" class="form-label">Πατρώνυμο </label>
                                <input class="form-control" type="text" id="patrwnymo_edit" name="patrwnymo">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-1">
                                <label for="dieythunsh_edit" class="form-label">Διεύθυνση </label>
                                <input type="text" id="dieythunsh_edit" class="form-control" name="dieythunsh">
                            </div>
                            <div class="col-md-2 mb-1">
                                <label for="arithmos_edit" class="form-label">Αριθμός </label>
                                <input type="text" id="arithmos_edit" class="form-control" name="arithmos">
                            </div>
                            <div class="col-md-4 mb-1">
                                <label for="perioxi_edit" class="form-label">Περιοχή </label>
                                <input type="text" id="perioxi_edit" class="form-control" name="perioxi">
                            </div>
                            <div class="col-md-2 mb-1">
                                <label for="tk_edit" class="form-label">Τ.Κ. </label>
                                <input type="text" id="tk_edit" class="form-control" name="tk">
                            </div>
                        </div>
    <!--                    <div class="row">-->
    <!--                        <div class="mb-1">-->
    <!--                            <label for="iban_edit" class="form-label">IBAN </label>-->
    <!--                            <input type="text" id="iban_edit" class="form-control" name="iban">-->
    <!--                        </div>-->
    <!--                    </div>-->
                        <div class="row">
                            <div class="mb-1">
                                <label for="security_edit" class="form-label">Άδεια Security </label>
                                <select class="form-select" id="security_edit" name="security">
                                    <option value="" selected></option>
                                    <option value="ΝΑΙ">ΝΑΙ</option>
                                    <option value="ΟΧΙ">ΟΧΙ</option>
    
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="mb-1">
                                <label for="eidikotita_edit" class="form-label">Ειδικότητα </label>
                                <select class="form-select" id="eidikotita_edit" name="eidikotita">
                                    <option value="" selected></option>
                                    <option value="ΦΥΛΑΚΑΣ">ΦΥΛΑΚΑΣ</option>
    
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="mb-1">
                                <label for="rating_edit" class="form-label">Αξιολόγηση </label>
                                <select class="form-select" id="rating_edit" name="rating">
                                    <option value=""></option>
                                    <option value="0">&#9734;</option>
                                    <option value="1">&#9733;</option>
                                    <option value="2">&#9733;&#9733;</option>
                                    <option value="3">&#9733;&#9733;&#9733;</option>
                                    <option value="4">&#9733;&#9733;&#9733;&#9733;</option>
                                    <option value="5">&#9733;&#9733;&#9733;&#9733;&#9733;</option>
                                </select>
                            </div>
                        </div>
                        <!--                    <div class="row">-->
                        <!--                        <div class="mb-1">-->
                        <!--                            <label for="katastasi" class="form-label">Κατάσταση </label>-->
                        <!--                            <select class="form-select" id="katastasi" name="katastasi">-->
                        <!--                                <option value="" selected></option>-->
                        <!--                                <option value="Αναμονή πρόσληψης">Αναμονή πρόσληψης</option>-->
                        <!--                                <option value="Προσλήφθηκε">Προσλήφθηκε</option>-->
                        <!--                                <option value="Απορρίφθηκε">Απορρίφθηκε</option>-->
                        <!--                            </select>-->
                        <!--                        </div>-->
                        <!--                    </div>-->
                        <input type="hidden" name="tablename" value="Resume">
                        <div class="row mt-4">
                            <div class="d-flex justify-content-end">
                                <div class="mb-1">
                                    <button type="reset" class="btn modal-cancel-btn" data-bs-dismiss="modal"
                                            aria-label="Close">
                                        Ακύρωση
                                    </button>
                                </div>
                                <div class="mb-1">
                                    <button type="submit" class="btn btn-danger">Αποθήκευση</button>
                                </div>
                            </div>
                        </div>
    
                    </form>
    
                </div>
            </div>
        </div>
    </div>
    
    <div class="modal modal-lg fade" id="notesModal" tabindex="-1"
         aria-labelledby="notesModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom: none">
                    <div class="d-flex justify-content-between w-100">
                        <h5 class="modal-title" id="notesModalModal"><strong>Επεξεργασία Βιογραφικού</strong></h5>
                        <button type="reset" class="custom-close-btn" data-bs-dismiss="modal" aria-label="Close">✖</button>
                    </div>
                </div>
                <div class="modal-body p-5">
                    <form method="POST" action="{% url 'createUpdateViografiko' %}">
                        {% csrf_token %}
                        <div class="mb-1"><input type="hidden" id="id_edit_notes" name="id" value="{{viografika.id}}"></div>
                        <div class="row">
                            <div class="col-md-12 mb-1">
                                <label for="notes_edit" class="form-label">Σημειώσεις</label>
                                <textarea name="notes" id="notes_edit" class="form-control" rows="5"></textarea>
                            </div>
                        </div>
    
                        <input type="hidden" name="tablename" value="Resume"/>
    
                        <div class="row mt-5">
                            <div class="d-flex justify-content-end">
                                <div class="mb-1">
                                    <button type="reset" class="btn modal-cancel-btn" data-bs-dismiss="modal"
                                            aria-label="Close">
                                        Ακύρωση
                                    </button>
                                </div>
                                <div class="mb-1">
                                    <button type="submit" class="btn btn-danger">Αποθήκευση</button>
                                </div>
                            </div>
                        </div>
    
                    </form>
    
                </div>
            </div>
        </div>
    </div>
    
    <div class="modal modal-lg fade" id="createInterviewModal" tabindex="-1"
         aria-labelledby="createInterviewModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom: none">
                    <div class="d-flex justify-content-between w-100">
                        <h5 class="modal-title" id="createInterviewModal_title"><strong></strong></h5>
                        <button type="reset" class="custom-close-btn" data-bs-dismiss="modal" aria-label="Close">✖</button>
                    </div>
                </div>
                <div class="modal-body pt-3 py-5 pb-5">
                    <form method="POST" action="">
                        {% csrf_token %}
                        <input type="hidden" id="id" name="id" value=""/>
                        <input type="hidden" name="tablename" value="Interview"/>
                        <div class="row justify-content-between mb-2">
    
                            <div class="col-md-6 mb-2 text-center">
                                <div>
                                    <span class="text-secondary fs-6">Προγραμματισμός Συνέντευξης με: </span>
                                </div>
                                <div class="px-4 py-2 red-bg-and-text-for-fullname">
                                    {{viografiko.first_name}} {{viografiko.last_name}}
                                </div>
                            </div>
                            <div class="col-md-6 mb-2">
                                <div>
                                    <label><span class="text-secondary fs-6">Αποστολή ειδοποίησης με:</span> </label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="send_notification_with_sms"
                                               name="send_notification_with_sms">
                                        <label class="form-check-label" for="send_notification_with_sms">
                                            SMS
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="send_notification_with_email"
                                               name="send_notification_with_email">
                                        <label class="form-check-label" for="send_notification_with_email">
                                            EMAIL
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-center mb-2">
                            <div class="col-md-4 mb-2">
                                <label for="date_interview" class="form-label">Ημερομηνία</label>
                                <input type="date" class="form-control custom-date-input" id="date_interview"
                                       name="date_interview"/>
                            </div>
                            <div class="col-md-4 mb-2">
                                <label for="time_interview" class="form-label">Ώρα</label>
                                <input type="time" class="form-control" id="time_interview" name="time_interview"/>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-12">
                                <label for="notes_edit" class="form-label">Σημειώσεις</label>
                                <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="row mt-5">
                            <div class="d-flex justify-content-end">
                                <div class="mb-1">
                                    <button type="reset" class="btn modal-cancel-btn" data-bs-dismiss="modal"
                                            aria-label="Close">
                                        Ακύρωση
                                    </button>
                                </div>
                                <div class="mb-1">
                                    <button type="submit" class="btn btn-danger">Αποθήκευση</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    
    
    
    {% endblock %}
