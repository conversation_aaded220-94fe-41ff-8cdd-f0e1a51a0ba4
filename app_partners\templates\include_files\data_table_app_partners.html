{% load static %}
{% load custom_filters %}
<div class="table-wrapper rounded-10 overflow-x-auto overflow-y-hidden">
    <table class="table table-hover"
           style="border-collapse: collapse"
           id="{{ table_id }}">
        <thead>
            <tr>
                {% for column in columns %}
                    <th scope="col"
                        class="py-4 px-2 column-{{ column.id }} {% if not column.default_visible and not column.required %}d-none{% endif %}"
                        data-column-id="{{ column.id }}">{{ column.label }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody class="bg-white">
            {% for obj in page_obj %}
                {% with outer_counter=forloop.counter %}
                    <tr>
                        {% for column in columns %}
                            <td class="column-{{ column.id }} {% if not column.default_visible and not column.required %}d-none{% endif %}">
                                {% if column.type == 'counter' %}
                                    {{ outer_counter }}
                                {% elif column.type == 'image' or column.type == 'file' %}
                                    {% with file_value=obj|get:column.field %}
                                        <div style="position: relative; display: inline-block;">
                                            {% if file_value %}
                                                {% if file_value|get_file_type == 'image' %}
                                                    <img src="{{ file_value.url }}"
                                                         alt="Image"
                                                         style="{{ column.style|default:'width: 100px;
                                                                height: 100px;
                                                                object-fit: cover;
                                                                ' }}">
                                                {% else %}
                                                    <a href="{{ file_value.url }}"
                                                       target="_blank"
                                                       class="btn btn-outline-dark rounded-pill btn-sm">
                                                        <span class="text-truncate d-inline-block"
                                                              style="max-width: 200px"
                                                              title="{{ file_value.name }}">
                                                            <i class="bi bi-file-earmark me-1"></i>{{ file_value.name|truncatechars:15 }}
                                                        </span>
                                                    </a>
                                                {% endif %}
                                                <div class="mt-2 d-flex gap-2 align-items-center justify-content-center">
                                                    <button type="button"
                                                            class="add-btn rounded-pill px-3 py-1"
                                                            onclick="uploadFile('{{ model_name }}', '{{ obj.id }}', '{{ column.field }}', '{{ column.type }}')">
                                                        <i class="bi bi-upload"></i>
                                                    </button>
                                                    <button type="button"
                                                            class="delete-btn rounded-pill px-3 py-1"
                                                            onclick="deleteFile1('{{ model_name }}', '{{ obj.id }}', '{{ column.field }}')">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                            {% else %}
                                                <span class="text-muted">No file</span>
                                                <div class="mt-2">
                                                    <button type="button"
                                                            class="add-btn rounded-pill px-3 py-1"
                                                            onclick="uploadFile('{{ model_name }}', '{{ obj.id }}', '{{ column.field }}')">
                                                        <i class="bi bi-upload"></i>
                                                    </button>
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% endwith %}
                                {% elif column.type == 'text' %}
                                    {{ obj|get:column.field }}
                                {% elif column.type == 'status' %}
                                    {% with value=obj|get:column.field %}
                                        <select class="form-select form-select-sm w-auto d-inline-block status-select rounded-pill border border-1 border-dark "
                                                data-model="{{ model_name }}"
                                                data-id="{{ obj.id }}"
                                                data-field="{{ column.field }}"
                                                onchange="updateSelectField(this)"
                                                role="button">
                                            {% for opt in column.options %}
                                                <option value="{{ opt.value|stringformat:'s' }}"
                                                        {% if opt.value|stringformat:'s' == value|stringformat:'s' %}selected{% endif %}>
                                                    {{ opt.label }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    {% endwith %}
                                {% elif column.type == 'select' %}
                                    {% with value=obj|get:column.field %}
                                        <select class="form-select form-select-sm w-auto d-inline-block rounded-pill border border-1 border-dark"
                                                data-model="{{ model_name }}"
                                                data-id="{{ obj.id }}"
                                                data-field="{{ column.field }}"
                                                onchange="updateSelectField(this)"
                                                role="button">
                                            {% for opt in column.options %}
                                                <option value="{{ opt.value }}"
                                                        {% if opt.value == value %}selected{% endif %}>
                                                    {{ opt.label }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    {% endwith %}
                                {% elif column.type == 'link' %}
                                    <a href="{{ column.url_prefix }}{{ obj.id }}"
                                       class="text-decoration-none text-dark">{{ obj|get:column.field }}</a>
                                {% elif column.type == 'actions' %}
                                    <div style="display: inline-flex;
                                                align-items: center;
                                                white-space: nowrap">
                                        <button class="btn btn-sm btn-outline-secondary custom-icon-btn me-2"
                                                onclick="openEditModal('{{ model_name }}', 'id', '{{ obj.id }}', 'editModalId')">
                                            <i class="bi bi-pencil-fill custom-icon-mint"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary custom-icon-btn"
                                                onclick="openDeleteModal('{{ model_name }}', '{{ obj.id }}')">
                                            <i class="bi bi-trash-fill custom-icon-mint"></i>
                                        </button>
                                    </div>
                                {% elif column.type == 'date' %}
                                    {{ obj|get:column.field|date:"d/m/Y" }}
                                {% elif column.type == 'datetime' %}
                                    {{ obj|get:column.field|date:"d/m/Y H:i" }}
                                {% endif %}
                            </td>
                        {% endfor %}
                    {% endwith %}
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
