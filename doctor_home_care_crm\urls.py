from django.contrib import admin
from django.urls import path, include
from django.views.generic import TemplateView, RedirectView
from doctor_home_care_crm.views import *
from app_users.views import auth_views


urlpatterns = [
    path('admin/', admin.site.urls),
    path('', RedirectView.as_view(url='/login/', permanent=False)),

# APPS
    path('partners/', include('app_partners.urls')), 
    path('users/', include('app_users.urls')),        
    path('msgtemplates/', include('app_msgtemplates.urls')), 
    path('patients/', include('app_patients.urls')),  
    path('prescriptions/', include('app_prescriptions.urls')),
    path('calls-center/', include('app_calls_center.urls')),

# AUTH
    path('login/', auth_views.login_user, name='login'),
    path('logout/', auth_views.logout_user, name='logout'),
    path('register/', auth_views.register, name='register'),


    # path('', RedirectView.as_view(url='/login/', permanent=False)),
    # path('login/', TemplateView.as_view(template_name='login.html'), name='login'),
    # path('register/', TemplateView.as_view(template_name='register.html'), name='register'),

    # Dummy forgot password flow
    # path('forgot_password/', TemplateView.as_view(template_name='forgot_password.html', extra_context={'forgot_password_step': 1}), name='forgot_password'),
    # path('forgot_password_step_2/', TemplateView.as_view(template_name='forgot_password.html', extra_context={'forgot_password_step': 2, 'email': '<EMAIL>'}), name='forgot_password_step_2'),
    
    # # Fix 405 error with custom class-based views
    # path('dummy/reset-password/', TemplateView.as_view(template_name='forgot_password.html', extra_context={'forgot_password_step': 2, 'email': '<EMAIL>'}), name='forgot-password-change-password-step-2'),
    # path('dashboard/', TemplateView.as_view(template_name='dashboard.html'), name='dashboard'),
    # path('patients/', TemplateView.as_view(template_name='patients.html'), name='patients'),

# API
    path('find_obj_with_id/', find_obj_with_id, name='find_obj_with_id'),
    path('find_objects_with_administratorId_and_userId/', find_objects_with_administratorId_and_userId,
         name='find_objects_with_administratorId_and_userId'),
    path('update_field/', update_field, name='update_field'),
    path('check_email_exists/', check_email_exists, name='check_email_exists'),
    path('check_tin_exists/', check_tin_exists, name='check_tin_exists'),
    path('check_amka_exists/', check_amka_exists, name='check_amka_exists'),
]
