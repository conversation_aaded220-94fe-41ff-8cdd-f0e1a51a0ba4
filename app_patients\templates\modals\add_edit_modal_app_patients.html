<div class="offcanvas offcanvas-end offcanvas-wide w-75 custom-offcanvas-rounded"
     tabindex="-1"
     id="{{ modal_id }}"
     aria-labelledby="{{ modal_id }}Label">
    <div class="offcanvas-header border-bottom py-4 px-4 flex-column align-items-center justify-content-center text-white mb-2"
         style="background-color: #6AC7BA">
        <h5 class="offcanvas-title mb-1" id="{{ modal_id }}Label">
            <strong>{{ modal_title }}</strong>
        </h5>
        <p class="mb-0 text-white">{{ modal_description }}</p>
        <button type="button"
                class="btn-close position-absolute top-0 end-0 mt-3 me-3 btn-close-white"
                data-bs-dismiss="offcanvas"
                aria-label="Close"></button>
    </div>
    <form id="{{ form_id }}"
          method="POST"
          action="{% url 'createUpdatePatient' %}"
          enctype="multipart/form-data"
          data-ajax-submit="true"
          class="d-flex flex-column h-100">
        {% csrf_token %}
        <input type="hidden" id="id_{{ mode }}" name="id" value="">
        <input type="hidden" name="tablename" value="{{ model_name }}">
        <!-- Scrollable content -->
        <div class="px-4 overflow-auto"
             style="flex: 1 1 auto;
                    max-height: calc(100vh - 200px)">
            <div class="row pt-2">
                {% for field in form_fields %}
                    <div class="col-md-{{ field.width|default:'12' }} mb-3">
                        <label for="{{ field.id }}_{{ mode }}"
                               class="form-label text-secondary fw-bold">{{ field.label }}</label>
                        {% if field.type == 'email' %}
                            <input type="email"
                                   id="{{ field.id }}_{{ mode }}"
                                   name="{{ field.name|default:field.id }}"
                                   class="form-control rounded-pill"
                                   placeholder="{{ field.placeholder }}"
                                   {% if field.required %}required{% endif %}>
                            <div class="invalid-feedback">Αυτό το email υπάρχει ήδη.</div>
                        {% elif field.type == 'text' %}
                            {% if field.id == 'amka' %}
                                <input type="text"
                                       id="{{ field.id }}_{{ mode }}"
                                       name="{{ field.name|default:field.id }}"
                                       class="form-control rounded-pill"
                                       inputmode="numeric"
                                       pattern="\d*"
                                       maxlength="11"
                                       oninput="this.value = this.value.replace(/\D/g, '')"
                                       placeholder="{{ field.placeholder }}"
                                       {% if field.required %}required{% endif %}>
                                <div class="invalid-feedback">Αυτό το ΑΜΚΑ υπάρχει ήδη.</div>
                            {% else %}
                                <input type="text"
                                       id="{{ field.id }}_{{ mode }}"
                                       name="{{ field.name|default:field.id }}"
                                       class="form-control rounded-pill"
                                       placeholder="{{ field.placeholder }}"
                                       {% if field.required %}required{% endif %}>
                            {% endif %}
                        {% elif field.type == 'password' %}
                            <div class="position-relative">
                                <input type="password"
                                       id="{{ field.id }}_{{ mode }}"
                                       name="{{ field.name|default:field.id }}"
                                       class="form-control rounded-pill pe-5"
                                       placeholder="{% if mode == 'edit' %}Αφήστε κενό για να διατηρήσετε τον υπάρχοντα κωδικό{% else %}{{ field.placeholder }}{% endif %}"
                                       {% if mode == 'add' and field.required %}required{% endif %}>
                                <button type="button"
                                        class="btn position-absolute end-0 top-50 translate-middle-y me-3 p-0 border-0 bg-transparent text-muted"
                                        onclick="togglePasswordVisibility('{{ field.id }}_{{ mode }}', this)">
                                    <i class="bi bi-eye-fill"></i>
                                </button>
                            </div>
                        {% elif field.type == 'select' %}
                            <select id="{{ field.id }}_{{ mode }}"
                                    name="{{ field.name|default:field.id }}"
                                    class="form-select rounded-pill"
                                    {% if field.required %}required{% endif %}>
                                <option value="">Επιλέξτε...</option>
                                {% for option in field.options %}<option value="{{ option.value }}">{{ option.label }}</option>{% endfor %}
                            </select>
                        {% elif field.type == 'date' %}
                            <input type="date"
                                   id="{{ field.id }}_{{ mode }}"
                                   name="{{ field.name|default:field.id }}"
                                   class="form-control rounded-pill"
                                   {% if field.required %}required{% endif %}>
                        {% elif field.type == 'textarea' %}
                            <textarea id="{{ field.id }}_{{ mode }}"
                                      name="{{ field.name|default:field.id }}"
                                      class="form-control rounded-3"
                                      rows="4"
                                      style="resize: vertical"
                                      {% if field.required %}required{% endif %}>{{ field.value }}</textarea>
                        {% elif field.type == 'file' %}
                            <input type="file"
                                   id="{{ field.id }}_{{ mode }}"
                                   name="{{ field.name|default:field.id }}"
                                   class="form-control"
                                   {% if field.required %}required{% endif %}>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>
        <!-- Fixed bottom button bar -->
        <div class="bg-white border-top py-3 px-4"
             style="position: sticky;
                    bottom: 0;
                    z-index: 1050">
            <div class="d-flex justify-content-end">
                <button type="reset"
                        class="btn btn-outline-dark me-2 rounded-pill px-4 py-2"
                        data-bs-dismiss="offcanvas">Ακύρωση</button>
                <button type="submit" class="btn btn-dark rounded-pill px-4 py-2">Αποθήκευση</button>
            </div>
        </div>
    </form>
</div>
