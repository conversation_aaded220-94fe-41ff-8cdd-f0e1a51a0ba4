
import os
import sys
from django.shortcuts import get_object_or_404
from django.db import models
import traceback
from django.core.serializers import serialize
import json


current_dir = os.getcwd()
sys.path.append(current_dir)
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)


from utilities import utils
from utilities import parameters as params
from local_settings import k
from utilities.token_manager import TokenManager
from utilities.messages import Messages
from utilities.status_code import StatusCode
from utilities.logs_manager import LogsManager

messages_obj = Messages()
status_code_obj = StatusCode()
logs_manager_obj = LogsManager(params.logs_dir)

def delete_entry_by_id(django_model, entry_id):
    try:
        entry = get_object_or_404(django_model, pk=entry_id)
        entry.delete()
        return True, 'H εγγραφή έχει διαγραφεί με επιτυχία!'
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return False, f'{e}'



def update_an_entry(django_model, django_form, form_data, entry_id):
    # post_request = request.POST
    form_update = None
    try:
        entry = get_object_or_404(django_model, pk=entry_id)
        # print(entry)
        form_update = django_form(form_data, instance=entry)

        form_update.id = entry.id
        if form_update.is_valid():
            form_update.save()
            return True, '', form_update
        else:
            return False, f'Μη έγκυρη υποβολή φόρμας: {form_update.errors}', form_update
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        form_errors = ''
        if form_update:
            form_errors = ''
            if form_update.errors:
                form_errors = f', form errors: {form_update.errors}'
        return False, f'(method: utils.update_an_entry) {e}{form_errors}.', form_update


def get_object_json_serializable(object):
    try:
        obj_str_json = serialize('json', object)
        obj_json = json.loads(obj_str_json)
        return obj_json
        # print('user_json ', user_json)
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return None

# def serialize_dict(data):
#     try:
#         json_string = json.dumps(data)
#         obj_json = json.loads(json_string)
#         return  obj_json
#         # print('user_json ', user_json)
#     except Exception as e:
#         traceback.print_exc()
#         logs_manager_obj.write_error_logs(traceback.format_exc())
#         return None

def get_user_json(user):
    try:
        user.password = None
        user_str_json = serialize('json', [user])[1:-1]
        user_json = json.loads(user_str_json)
        return user_json
        # print('user_json ', user_json)
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return None


def model_entry_save_or_return_error_str(entry): #model entry or form
    try:
        entry.save()
        return True, ''
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return False, f'{e}'

def get_object_from_model_by_id(model, id, return_in_except=None):
    try:
        obj = get_object_or_404(model, pk=id)
        return obj
    except Exception as e:
        # traceback.    print_exc()
        return return_in_except




def delete_all_entries_of_a_db_table_model_by_email(table_model, email):
    instance_list = get_model_instance_by_email(model=table_model, email=email, first_instance_only=False)
    # instance_list = table_model.objects.filter(email=email)
    for inst in instance_list:
        inst.delete()


def check_email_validation(db_model, email, en_or_gr):
    try:
        instance = get_model_instance_by_email(model=db_model, email=email, first_instance_only=True)
        # instance = User.objects.filter(email=email)[0]
        if instance:
            return True, messages_obj.valid_email.get(en_or_gr), 200
        else:
            return False, messages_obj.invalid_email.get(en_or_gr), status_code_obj.unauthorized_401
    except Exception as e:
        logs_manager_obj.write_error_logs(traceback.format_exc())
        utils.exception_traceback_print()
        return False, messages_obj.exception(e, en_or_gr), status_code_obj.not_acceptable_406

def check_email_verification_code_validation(verification_codes_db_model, email, code, en_or_gr):
    try:
        # print(email)
        # print(code)
        instance = None
        try:
            instance = get_model_instance_by_email_code(model=verification_codes_db_model, email=email, code=code,
                                                        first_instance_only=True)
            # instance = Verification_codes.objects.filter(email=email, code=code)[0]
        except Exception as e:
            instance = None
            # print('error: ', e)
            utils.exception_traceback_print()
            logs_manager_obj.write_error_logs(traceback.format_exc())
        if instance:
            created_at = instance.created_at
            code_has_expired = params.token_has_expired(time_token_created=created_at,
                                                        expires_in_minutes=params.change_password_verification_code_expiration_in_minutes,
                                                        expires_in_hours=0, expires_in_days=0)
            if code_has_expired:
                return False, messages_obj.verification_code_has_expired.get(
                    en_or_gr), status_code_obj.unauthorized_401
            else:
                return True, messages_obj.verification_code_valid.get(en_or_gr), 200
        else:
            return False, messages_obj.verification_code_invalid.get(en_or_gr), status_code_obj.unauthorized_401
    except Exception as e:
        logs_manager_obj.write_error_logs(traceback.format_exc())
        traceback.print_exc()
        return False, messages_obj.exception(e, en_or_gr), status_code_obj.not_acceptable_406


def check_forgot_password_code_validation(verification_codes_db_model, email, code, en_or_gr):
    try:
        # print(email)
        # print(code)
        instance = None
        try:
            instance = get_model_instance_by_email_code(model=verification_codes_db_model, email=email, code=code,
                                                        first_instance_only=True)
            # instance = Verification_codes.objects.filter(email=email, code=code)[0]
        except Exception as e:
            instance = None
            # print('error: ', e)
            utils.exception_traceback_print()
            logs_manager_obj.write_error_logs(traceback.format_exc())
        if instance:
            created_at = instance.created_at
            code_has_expired = params.token_has_expired(time_token_created=created_at,
                                                        expires_in_minutes=params.change_password_verification_code_expiration_in_minutes,
                                                        expires_in_hours=0, expires_in_days=0)
            if code_has_expired:
                return False, messages_obj.verification_code_has_expired.get(
                    en_or_gr), status_code_obj.unauthorized_401
            else:
                return True, messages_obj.verification_code_valid.get(en_or_gr), 200
        else:
            return False, messages_obj.verification_code_invalid.get(en_or_gr), status_code_obj.unauthorized_401
    except Exception as e:
        logs_manager_obj.write_error_logs(traceback.format_exc())
        traceback.print_exc()
        return False, messages_obj.exception(e, en_or_gr), status_code_obj.not_acceptable_406


def check_token_validation(token_db_model, token, email, en_or_gr):
    try:
        manage_token = TokenManager()
        if token:
            dec_token = manage_token.decod_token(token, k.p, k.S_32bytes)
            instance = manage_token.get_model_instance_by_email_token(model=token_db_model, email=email, token=dec_token,
                                                         first_instance_only=True)
            # instance = TokenModel.objects.filter(token=dec_token, email=email)[0]
            if instance:
                return True, messages_obj.valid_token.get(en_or_gr), 200
            else:
                return False, messages_obj.invalid_token.get(en_or_gr), status_code_obj.unauthorized_401
        else:
            return False, messages_obj.null_token.get(en_or_gr), status_code_obj.bad_request_400
    except Exception as e:
        logs_manager_obj.write_error_logs(traceback.format_exc())
        traceback.print_exc()
        return False, messages_obj.exception(e, en_or_gr), status_code_obj.not_acceptable_406





def get_model_instance_by_email_tel(model, email, tel, first_instance_only=False):
    if first_instance_only:
        try:
            return model.objects.filter(email=email, tel=tel)[0]
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return None
    else:
        try:
            return model.objects.filter(email=email, tel=tel)
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return []





def get_model_instance_by_email_code(model, email, code, first_instance_only=False):
    if first_instance_only:
        try:
            # print(email, code)
            return model.objects.filter(code=code, email=email)[0]
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return None
    else:
        try:
            return model.objects.filter(code=code, email=email)
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return []


def get_model_instance_by_email(model, email, first_instance_only=False):
    if first_instance_only:
        try:
            return model.objects.filter(email=email)[0]
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return None
    else:
        try:
            return model.objects.filter(email=email)
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return []


def get_model_instance_by_tel(model, tel, first_instance_only=False):
    if first_instance_only:
        try:
            return model.objects.filter(tel=tel)[0]
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return None
    else:
        try:
            return model.objects.filter(tel=tel)
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return []


def get_model_instance_by_afm(model, afm, first_instance_only=False):
    if first_instance_only:
        try:
            return model.objects.filter(afm=afm)[0]
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return None
    else:
        try:
            return model.objects.filter(afm=afm)
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return []


def get_model_instance_by_username(model, username, first_instance_only=False):
    if first_instance_only:
        try:
            return model.objects.filter(username=username)[0]
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return None
    else:
        try:
            return model.objects.filter(username=username)
        except Exception as e:
            traceback.print_exc()
            logs_manager_obj.write_error_logs(traceback.format_exc())
            return []


def exist_other_entry_with_different_email_and_this_afm(model, email, afm, en_or_gr):
    try:
        e = model.objects.exclude(email=email).filter(afm=afm).exists()
        if e:
            return True, messages_obj.user_with_other_email_and_this_afm_already_exists(afm=afm, en_or_gr=en_or_gr)
        return False, f'Αυτό το ΑΦΜ ({afm}) δεν είναι καταχωρημένο σε άλλη εγγραφή.'
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return True, messages_obj.exception(e, en_or_gr=en_or_gr)


def exist_other_entry_with_this_username(model, current_username, new_username, en_or_gr):
    try:
        e = model.objects.exclude(username=current_username).filter(username=new_username).exists()
        if e:
            return True, messages_obj.user_with_this_username_already_exists(username=new_username, en_or_gr=en_or_gr)
        m_el = f'Αυτό το username ({new_username}) είναι διαθέσιμο.'
        m_en = f'This username ({new_username}) is available.'
        return False, utils.text_in_selected_language(m_el, m_en, en_or_gr)
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return True, messages_obj.exception(e, en_or_gr=en_or_gr)

def exist_other_entry_with_this_email(model, current_email, new_email, en_or_gr):
    try:
        e = model.objects.exclude(email=current_email).filter(username=new_email).exists()
        if e:
            return True, messages_obj.user_with_this_email_already_exists(email=new_email, en_or_gr=en_or_gr)
        m_el = f'Αυτό το e-mail ({new_email}) είναι διαθέσιμο.'
        m_en = f'This e-mail ({new_email}) is available.'
        return False, utils.text_in_selected_language(m_el, m_en, en_or_gr)
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return True, messages_obj.exception(e, en_or_gr=en_or_gr)

def exist_other_entry_with_different_email_and_this_tel(model, email, tel, en_or_gr):
    try:
        e = model.objects.exclude(email=email).filter(tel=tel).exists()
        if e:
            return True, messages_obj.user_with_other_email_and_this_tel_already_exists(tel=tel, en_or_gr=en_or_gr)
        return False, f'Αυτό το τηλέφωνο ({tel}) δεν είναι καταχωρημένο σε άλλη εγγραφή.'
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return True, messages_obj.exception(e, en_or_gr=en_or_gr)


def exist_other_entry_with_different_email_and_this_pei(model, email, pei, en_or_gr):
    try:
        e = model.objects.exclude(email=email).filter(pei=pei).exists()
        if e:
            return True, messages_obj.user_with_other_email_and_this_pei_already_exists(pei=pei, en_or_gr=en_or_gr)
        return False, f'Αυτός ο αριθμός ΠΕΙ ({pei}) δεν είναι καταχωρημένο σε άλλη εγγραφή.'
    except Exception as e:
        traceback.print_exc()
        logs_manager_obj.write_error_logs(traceback.format_exc())
        return True, messages_obj.exception(e, en_or_gr=en_or_gr)
